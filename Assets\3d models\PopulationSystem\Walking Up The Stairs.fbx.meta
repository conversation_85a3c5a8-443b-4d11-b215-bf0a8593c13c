fileFormatVersion: 2
guid: 08ba59d1cb0f09643ac18861e7ac23b8
ModelImporter:
  serializedVersion: 26
  internalIDToNameTable:
  - first:
      74: -203655887218126122
    second: mixamo.com
  externalObjects: {}
  materials:
    importMaterials: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: mixamo.com
      takeName: mixamo.com
      internalID: -203655887218126122
      firstFrame: 0
      lastFrame: 36
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    serializedVersion: 3
    human:
    - boneName: mixamorig:Hips
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftUpLeg
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightUpLeg
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftLeg
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightLeg
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftFoot
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightFoot
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:Spine
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:Spine1
      humanName: Chest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:Neck
      humanName: Neck
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:Head
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftShoulder
      humanName: LeftShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightShoulder
      humanName: RightShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftArm
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightArm
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftForeArm
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightForeArm
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHand
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHand
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftToeBase
      humanName: LeftToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightToeBase
      humanName: RightToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandThumb1
      humanName: Left Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandThumb2
      humanName: Left Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandThumb3
      humanName: Left Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandIndex1
      humanName: Left Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandIndex2
      humanName: Left Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandIndex3
      humanName: Left Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandMiddle1
      humanName: Left Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandMiddle2
      humanName: Left Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandMiddle3
      humanName: Left Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandRing1
      humanName: Left Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandRing2
      humanName: Left Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandRing3
      humanName: Left Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandPinky1
      humanName: Left Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandPinky2
      humanName: Left Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:LeftHandPinky3
      humanName: Left Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandThumb1
      humanName: Right Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandThumb2
      humanName: Right Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandThumb3
      humanName: Right Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandIndex1
      humanName: Right Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandIndex2
      humanName: Right Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandIndex3
      humanName: Right Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandMiddle1
      humanName: Right Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandMiddle2
      humanName: Right Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandMiddle3
      humanName: Right Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandRing1
      humanName: Right Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandRing2
      humanName: Right Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandRing3
      humanName: Right Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandPinky1
      humanName: Right Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandPinky2
      humanName: Right Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:RightHandPinky3
      humanName: Right Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: mixamorig:Spine2
      humanName: UpperChest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: Walking Up The Stairs(Clone)
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Beta_Joints
      parentName: Walking Up The Stairs(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Beta_Surface
      parentName: Walking Up The Stairs(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:Hips
      parentName: Walking Up The Stairs(Clone)
      position: {x: 0.0023673968, y: 1.0720448, z: 0.017423373}
      rotation: {x: -0.0000000014551915, y: 7.940934e-19, z: -5.456968e-10, w: 1}
      scale: {x: 1, y: 0.9999999, z: 1}
    - name: mixamorig:Spine
      parentName: mixamorig:Hips
      position: {x: -0.00000009234152, y: 0.10181588, z: 0.0013152092}
      rotation: {x: 0.0000000130967255, y: 0.000000002910383, z: 9.094947e-10, w: 1}
      scale: {x: 0.99999994, y: 0.9999999, z: 1}
    - name: mixamorig:Spine1
      parentName: mixamorig:Spine
      position: {x: -0.000000002519403, y: 0.10083451, z: -0.010008043}
      rotation: {x: -0.000000011641534, y: -0.000000008731148, z: 3.6379785e-10, w: 1}
      scale: {x: 0.99999994, y: 0.99999994, z: 0.9999999}
    - name: mixamorig:Spine2
      parentName: mixamorig:Spine1
      position: {x: -0.0000000034574863, y: 0.09100011, z: -0.013734171}
      rotation: {x: 0.000000011641532, y: -0.000000002910384, z: -0.0000000050931703,
        w: 1}
      scale: {x: 1, y: 1, z: 0.99999994}
    - name: mixamorig:Neck
      parentName: mixamorig:Spine2
      position: {x: -0.0000000063342855, y: 0.16671668, z: -0.025161678}
      rotation: {x: -0.000000011641532, y: 0.000000020372681, z: 0.0000000036379786,
        w: 1}
      scale: {x: 1.0000001, y: 0.99999994, z: 1}
    - name: mixamorig:Head
      parentName: mixamorig:Neck
      position: {x: 0.0000000042423185, y: 0.09617875, z: 0.016850075}
      rotation: {x: -0.000000005820768, y: -0.000000010913936, z: 3.6379777e-10, w: 1}
      scale: {x: 1, y: 1.0000001, z: 1}
    - name: mixamorig:HeadTop_End
      parentName: mixamorig:Head
      position: {x: 0.0000000065082415, y: 0.17815155, z: 0.025849855}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftEye
      parentName: mixamorig:Head
      position: {x: -0.030675607, y: 0.06409507, z: 0.09283554}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightEye
      parentName: mixamorig:Head
      position: {x: 0.030675504, y: 0.064095, z: 0.09283547}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftShoulder
      parentName: mixamorig:Spine2
      position: {x: -0.045704465, y: 0.10945985, z: -0.02627988}
      rotation: {x: -0, y: 0.000000008731149, z: 3.7632657e-16, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftArm
      parentName: mixamorig:LeftShoulder
      position: {x: -0.1059237, y: -0.005245829, z: -0.0223212}
      rotation: {x: -0.000000011641532, y: -0, z: -0.000000016007109, w: 1}
      scale: {x: 1, y: 1.0000001, z: 1}
    - name: mixamorig:LeftForeArm
      parentName: mixamorig:LeftArm
      position: {x: -0.2784152, y: -0.0000008942865, z: 0.0000003745891}
      rotation: {x: 0.000000005820766, y: -2.7952087e-16, z: 0.000000032014214, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHand
      parentName: mixamorig:LeftForeArm
      position: {x: -0.2832884, y: -0.00000017440718, z: 0.00000037804523}
      rotation: {x: -0.000000005820767, y: -0.000000023283064, z: 1.3552527e-16, w: 1}
      scale: {x: 1.0000005, y: 1.0000004, z: 1}
    - name: mixamorig:LeftHandThumb1
      parentName: mixamorig:LeftHand
      position: {x: -0.024661401, y: -0.015750492, z: 0.026824135}
      rotation: {x: -0.11863639, y: 0.08685277, z: -0.11863634, w: 0.9819916}
      scale: {x: 1.0000002, y: 1.0000002, z: 1.0000001}
    - name: mixamorig:LeftHandThumb2
      parentName: mixamorig:LeftHandThumb1
      position: {x: -0.032298036, y: -0.018646907, z: 0.018646812}
      rotation: {x: 0.0000000058207648, y: -0, z: 0.000000016734703, w: 1}
      scale: {x: 0.9999998, y: 0.99999976, z: 1.0000001}
    - name: mixamorig:LeftHandThumb3
      parentName: mixamorig:LeftHandThumb2
      position: {x: -0.02652565, y: -0.015314929, z: 0.015314716}
      rotation: {x: -0.0000000029103842, y: 0.000000005820768, z: 0.0000000034560816,
        w: 1}
      scale: {x: 0.9999998, y: 0.9999998, z: 1.0000001}
    - name: mixamorig:LeftHandThumb4
      parentName: mixamorig:LeftHandThumb3
      position: {x: -0.019545669, y: -0.0112850005, z: 0.011284921}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandIndex1
      parentName: mixamorig:LeftHand
      position: {x: -0.09109301, y: -0.005170834, z: 0.02260073}
      rotation: {x: -0, y: -3.5527137e-15, z: -0.0000000029103795, w: 1}
      scale: {x: 1.0000007, y: 1.0000007, z: 1.0000001}
    - name: mixamorig:LeftHandIndex2
      parentName: mixamorig:LeftHandIndex1
      position: {x: -0.036764093, y: -0.00000006939138, z: 0.00000029242517}
      rotation: {x: -0.000000011641533, y: 0.000000023283068, z: -0.000000014551919,
        w: 1}
      scale: {x: 1.000001, y: 1.0000007, z: 0.99999994}
    - name: mixamorig:LeftHandIndex3
      parentName: mixamorig:LeftHandIndex2
      position: {x: -0.028830625, y: 0.00000038511968, z: -0.00000042764816}
      rotation: {x: 0.000000034924597, y: 0.000000017462298, z: 0.000000011641535,
        w: 1}
      scale: {x: 1.0000007, y: 1.000001, z: 1.0000001}
    - name: mixamorig:LeftHandIndex4
      parentName: mixamorig:LeftHandIndex3
      position: {x: -0.023927199, y: 0.00000037025907, z: 0.00000033554434}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandMiddle1
      parentName: mixamorig:LeftHand
      position: {x: -0.095334016, y: -0.00000033924047, z: 0.00000013937026}
      rotation: {x: 4.440892e-16, y: 0.000000023283064, z: -0.00000001164153, w: 1}
      scale: {x: 1.0000005, y: 1.0000006, z: 1.0000001}
    - name: mixamorig:LeftHandMiddle2
      parentName: mixamorig:LeftHandMiddle1
      position: {x: -0.036982365, y: 0.00000001961359, z: 0.00000017916967}
      rotation: {x: 0.000000011641532, y: -0, z: -0.000000002910383, w: 1}
      scale: {x: 1.0000011, y: 1.000001, z: 0.9999999}
    - name: mixamorig:LeftHandMiddle3
      parentName: mixamorig:LeftHandMiddle2
      position: {x: -0.029509215, y: -0.00000039376084, z: 0.0000001644796}
      rotation: {x: -0.000000011641532, y: -0.000000005820766, z: 0.000000016007105,
        w: 1}
      scale: {x: 1.000001, y: 1.0000013, z: 1}
    - name: mixamorig:LeftHandMiddle4
      parentName: mixamorig:LeftHandMiddle3
      position: {x: -0.028339025, y: -0.0000003893482, z: -0.00000009504223}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandRing1
      parentName: mixamorig:LeftHand
      position: {x: -0.091045275, y: -0.00043939505, z: -0.018650847}
      rotation: {x: -0.000000034924597, y: 0.00000001164153, z: -0.000000011641531,
        w: 1}
      scale: {x: 1, y: 1.0000001, z: 0.99999994}
    - name: mixamorig:LeftHandRing2
      parentName: mixamorig:LeftHandRing1
      position: {x: -0.031540267, y: -0.00000006553468, z: 0.00000010425744}
      rotation: {x: 0.00000004656613, y: 1.7763568e-15, z: 0.000000020372681, w: 1}
      scale: {x: 1.0000005, y: 1.0000004, z: 1.0000001}
    - name: mixamorig:LeftHandRing3
      parentName: mixamorig:LeftHandRing2
      position: {x: -0.029376723, y: -0.00000045458617, z: -0.00000006864757}
      rotation: {x: -0.000000011641532, y: 0.000000021827873, z: 0.0000000014551923,
        w: 1}
      scale: {x: 1.0000012, y: 1.0000005, z: 1}
    - name: mixamorig:LeftHandRing4
      parentName: mixamorig:LeftHandRing3
      position: {x: -0.026474526, y: -0.00000049804413, z: 7.0705153e-10}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftHandPinky1
      parentName: mixamorig:LeftHand
      position: {x: -0.08077778, y: -0.004886682, z: -0.03806067}
      rotation: {x: 1.3322676e-15, y: 0.000000034924597, z: -0.000000008731147, w: 1}
      scale: {x: 1.0000004, y: 1.0000007, z: 0.99999994}
    - name: mixamorig:LeftHandPinky2
      parentName: mixamorig:LeftHandPinky1
      position: {x: -0.036000345, y: -0.000000022522151, z: -0.00000026238794}
      rotation: {x: -0.000000011641532, y: 0.0000000072759576, z: 3.6379788e-10, w: 1}
      scale: {x: 1.0000008, y: 1.0000007, z: 1.0000002}
    - name: mixamorig:LeftHandPinky3
      parentName: mixamorig:LeftHandPinky2
      position: {x: -0.021142116, y: 0.00000019538281, z: -0.00000002864415}
      rotation: {x: 0.000000023283064, y: -0.000000013096724, z: -0.00000001200533,
        w: 1}
      scale: {x: 1.0000002, y: 1.0000001, z: 1}
    - name: mixamorig:LeftHandPinky4
      parentName: mixamorig:LeftHandPinky3
      position: {x: -0.019756826, y: 0.00000012405657, z: -0.00000044301405}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightShoulder
      parentName: mixamorig:Spine2
      position: {x: 0.045699697, y: 0.10946176, z: -0.026280174}
      rotation: {x: -0.000000023283064, y: 0.000000002910383, z: 0.0000000043655746,
        w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightArm
      parentName: mixamorig:RightShoulder
      position: {x: 0.105928436, y: -0.0052479836, z: -0.022320986}
      rotation: {x: 0.000000005820766, y: 0.0000000029103826, z: 0.0000000072759585,
        w: 1}
      scale: {x: 1.0000001, y: 1.0000002, z: 1.0000001}
    - name: mixamorig:RightForeArm
      parentName: mixamorig:RightArm
      position: {x: 0.2784152, y: -0.00000033079218, z: 0.000000116763104}
      rotation: {x: -0, y: 0.0000000029103835, z: -0.000000013096724, w: 1}
      scale: {x: 1.0000002, y: 1, z: 1}
    - name: mixamorig:RightHand
      parentName: mixamorig:RightForeArm
      position: {x: 0.2832884, y: -0.0000000015814171, z: 0.00000055816014}
      rotation: {x: 0.000000005820766, y: -0, z: -0.000000005820766, w: 1}
      scale: {x: 1.0000006, y: 1.0000005, z: 1.0000005}
    - name: mixamorig:RightHandPinky1
      parentName: mixamorig:RightHand
      position: {x: 0.080766745, y: -0.0048845927, z: -0.038060103}
      rotation: {x: 0.00000004656613, y: 0.000000011641534, z: 0.000000013096723,
        w: 1}
      scale: {x: 1.0000005, y: 1.0000006, z: 1}
    - name: mixamorig:RightHandPinky2
      parentName: mixamorig:RightHandPinky1
      position: {x: 0.036000345, y: 0.00000039676655, z: 0.0000005081442}
      rotation: {x: -0.000000023283064, y: -8.881784e-16, z: 0.000000021827873, w: 1}
      scale: {x: 1.000001, y: 1.000001, z: 1}
    - name: mixamorig:RightHandPinky3
      parentName: mixamorig:RightHandPinky2
      position: {x: 0.021142116, y: 0.00000015137925, z: 0.00000014793768}
      rotation: {x: -0.000000011641532, y: -0.000000011641531, z: -3.8237642e-15,
        w: 1}
      scale: {x: 1.0000011, y: 1.0000014, z: 1.0000001}
    - name: mixamorig:RightHandPinky4
      parentName: mixamorig:RightHandPinky3
      position: {x: 0.019756826, y: -0.00000018521958, z: -0.000000009839841}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandRing1
      parentName: mixamorig:RightHand
      position: {x: 0.09103588, y: -0.00043914348, z: -0.018650724}
      rotation: {x: -0, y: 0.000000007275957, z: 0.000000016007105, w: 1}
      scale: {x: 1.0000004, y: 1.0000005, z: 1.0000002}
    - name: mixamorig:RightHandRing2
      parentName: mixamorig:RightHandRing1
      position: {x: 0.031540267, y: 0.00000023997296, z: 0.0000004885121}
      rotation: {x: -9.31736e-17, y: -0.000000013096722, z: -0.000000016007105, w: 1}
      scale: {x: 1.0000004, y: 1.0000004, z: 1}
    - name: mixamorig:RightHandRing3
      parentName: mixamorig:RightHandRing2
      position: {x: 0.029376723, y: -0.00000041600654, z: -0.00000039660634}
      rotation: {x: -0.000000046566136, y: -0.0000000058207696, z: 0.00000005820766,
        w: 1}
      scale: {x: 1.0000001, y: 1.0000004, z: 1}
    - name: mixamorig:RightHandRing4
      parentName: mixamorig:RightHandRing3
      position: {x: 0.026474526, y: 0.00000024608212, z: -0.000000022949234}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandMiddle1
      parentName: mixamorig:RightHand
      position: {x: 0.09532503, y: -0.00000016312302, z: 0.00000016791842}
      rotation: {x: 0.000000023283064, y: 2.2022855e-16, z: 0.0000000145519135, w: 1}
      scale: {x: 1.0000008, y: 1.0000008, z: 1.0000004}
    - name: mixamorig:RightHandMiddle2
      parentName: mixamorig:RightHandMiddle1
      position: {x: 0.036982365, y: 0.00000015066959, z: -0.00000022545524}
      rotation: {x: -0.00000004656613, y: -0.000000008731148, z: 1.0914663e-15, w: 1}
      scale: {x: 1.0000005, y: 1.0000007, z: 1.0000001}
    - name: mixamorig:RightHandMiddle3
      parentName: mixamorig:RightHandMiddle2
      position: {x: 0.029509215, y: -0.000000003113245, z: -0.0000000030340255}
      rotation: {x: 0.000000023283064, y: 0.00000002037268, z: 0.000000008731147,
        w: 1}
      scale: {x: 1.0000002, y: 1.0000005, z: 1.0000002}
    - name: mixamorig:RightHandMiddle4
      parentName: mixamorig:RightHandMiddle3
      position: {x: 0.028339025, y: 0.00000008053241, z: 0.00000053787704}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandIndex1
      parentName: mixamorig:RightHand
      position: {x: 0.09108281, y: -0.0051678894, z: 0.022601163}
      rotation: {x: -0, y: 3.388131e-17, z: 0.000000005820765, w: 1}
      scale: {x: 1.0000005, y: 1.0000006, z: 1}
    - name: mixamorig:RightHandIndex2
      parentName: mixamorig:RightHandIndex1
      position: {x: 0.036764093, y: 0.00000019227156, z: 0.0000003235567}
      rotation: {x: -3.1763732e-17, y: -0.0000000054569687, z: 0.0000000170985, w: 1}
      scale: {x: 1.0000006, y: 1.0000007, z: 1}
    - name: mixamorig:RightHandIndex3
      parentName: mixamorig:RightHandIndex2
      position: {x: 0.028830625, y: -0.00000049601215, z: 0.00000040448975}
      rotation: {x: 0.00000004656613, y: 0.000000022919266, z: -0.000000011277736,
        w: 1}
      scale: {x: 1.0000006, y: 1.0000005, z: 1.0000001}
    - name: mixamorig:RightHandIndex4
      parentName: mixamorig:RightHandIndex3
      position: {x: 0.023927199, y: 0.000000268147, z: 0.00000028020133}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightHandThumb1
      parentName: mixamorig:RightHand
      position: {x: 0.024648061, y: -0.015727142, z: 0.026826264}
      rotation: {x: -0.11863686, y: -0.08684765, z: 0.11863691, w: 0.9819919}
      scale: {x: 1.0000004, y: 1.0000002, z: 1.0000002}
    - name: mixamorig:RightHandThumb2
      parentName: mixamorig:RightHandThumb1
      position: {x: 0.03229772, y: -0.018647088, z: 0.018647185}
      rotation: {x: 0.00000002328306, y: -0.000000017462298, z: -0.00000003783498,
        w: 1}
      scale: {x: 0.9999998, y: 0.99999994, z: 1}
    - name: mixamorig:RightHandThumb3
      parentName: mixamorig:RightHandThumb2
      position: {x: 0.02652605, y: -0.015314492, z: 0.015314456}
      rotation: {x: -0, y: 0.000000005820767, z: -0.00000003783498, w: 1}
      scale: {x: 1.0000002, y: 1.0000002, z: 1}
    - name: mixamorig:RightHandThumb4
      parentName: mixamorig:RightHandThumb3
      position: {x: 0.019545805, y: -0.011284617, z: 0.01128507}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightUpLeg
      parentName: mixamorig:Hips
      position: {x: 0.08207796, y: -0.06751662, z: -0.015995594}
      rotation: {x: 0.0000000014551915, y: -0.0000000014551917, z: 5.456968e-10, w: 1}
      scale: {x: 1, y: 1.0000002, z: 1.0000001}
    - name: mixamorig:RightLeg
      parentName: mixamorig:RightUpLeg
      position: {x: -8.435572e-10, y: -0.44370535, z: 0.0028615638}
      rotation: {x: -0, y: -0, z: -0.000000004365575, w: 1}
      scale: {x: 0.9999999, y: 1.0000001, z: 1.0000002}
    - name: mixamorig:RightFoot
      parentName: mixamorig:RightLeg
      position: {x: -0.000000009644582, y: -0.4442773, z: -0.02983789}
      rotation: {x: -0.00000004656613, y: 0.000000002910383, z: 0.0000000058207665,
        w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:RightToeBase
      parentName: mixamorig:RightFoot
      position: {x: 0.00000002360438, y: -0.08728669, z: 0.107105605}
      rotation: {x: 0.00000006984919, y: -0.0000000021827873, z: 3.3881334e-17, w: 1}
      scale: {x: 1, y: 1.0000002, z: 1}
    - name: mixamorig:RightToe_End
      parentName: mixamorig:RightToeBase
      position: {x: 0.000000020942645, y: -0.000006763694, z: 0.0927812}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftUpLeg
      parentName: mixamorig:Hips
      position: {x: -0.082077816, y: -0.06751714, z: -0.015995556}
      rotation: {x: -0.000000021827873, y: -0.000000005820766, z: 0.0000000034560799,
        w: 1}
      scale: {x: 1, y: 0.9999998, z: 1.0000001}
    - name: mixamorig:LeftLeg
      parentName: mixamorig:LeftUpLeg
      position: {x: 0.000000004110158, y: -0.44370472, z: 0.0028464263}
      rotation: {x: 0.00000005820766, y: -1.3322676e-15, z: -0.0000000029103833, w: 1}
      scale: {x: 1, y: 1.0000006, z: 0.9999998}
    - name: mixamorig:LeftFoot
      parentName: mixamorig:LeftLeg
      position: {x: -0.0000000047163935, y: -0.44427872, z: -0.029821906}
      rotation: {x: -0.000000034924597, y: 0.0000000058207674, z: 2.2737251e-11, w: 1}
      scale: {x: 1.0000001, y: 0.99999994, z: 1.0000001}
    - name: mixamorig:LeftToeBase
      parentName: mixamorig:LeftFoot
      position: {x: 0.000000029609879, y: -0.087286696, z: 0.1071056}
      rotation: {x: -7.2759576e-10, y: -0.000000005820767, z: 0.0000000012505553,
        w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: mixamorig:LeftToe_End
      parentName: mixamorig:LeftToeBase
      position: {x: 0.000000025080618, y: -0.0000067668298, z: 0.09278136}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 1
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 3
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
