fileFormatVersion: 2
guid: be675302c726a42448a335eee6f52a39
ModelImporter:
  serializedVersion: 16
  fileIDToRecycleName:
    100000: //RootNode
    100002: SEAGULL_
    100004: SEAGULL_ Head
    100006: SEAGULL_ L Calf
    100008: SEAGULL_ L Clavicle
    100010: SEAGULL_ L Foot
    100012: SEAGULL_ L Forearm
    100014: SEAGULL_ L Hand
    100016: SEAGULL_ L HorseLink
    100018: SEAGULL_ L Thigh
    100020: SEAGULL_ L Toe0
    100022: SEAGULL_ L Toe01
    100024: SEAGULL_ L Toe02
    100026: SEAGULL_ L UpperArm
    100028: SEAGULL_ Neck
    100030: SEAGULL_ Pelvis
    100032: SEAGULL_ Queue de cheval 1
    100034: SEAGULL_ R Calf
    100036: SEAGULL_ R Clavicle
    100038: SEAGULL_ R Foot
    100040: SEAGULL_ R Forearm
    100042: SEAGULL_ R Hand
    100044: SEAGULL_ R HorseLink
    100046: SEAGULL_ R Thigh
    100048: SEAGULL_ R Toe0
    100050: SEAGULL_ R Toe01
    100052: SEAGULL_ R Toe02
    100054: SEAGULL_ R UpperArm
    100056: SEAGULL_ Spine
    100058: SEAGULL_ Tail
    100060: SEAGULL_ TailL
    100062: SEAGULL_ TailR
    400000: //RootNode
    400002: SEAGULL_
    400004: SEAGULL_ Head
    400006: SEAGULL_ L Calf
    400008: SEAGULL_ L Clavicle
    400010: SEAGULL_ L Foot
    400012: SEAGULL_ L Forearm
    400014: SEAGULL_ L Hand
    400016: SEAGULL_ L HorseLink
    400018: SEAGULL_ L Thigh
    400020: SEAGULL_ L Toe0
    400022: SEAGULL_ L Toe01
    400024: SEAGULL_ L Toe02
    400026: SEAGULL_ L UpperArm
    400028: SEAGULL_ Neck
    400030: SEAGULL_ Pelvis
    400032: SEAGULL_ Queue de cheval 1
    400034: SEAGULL_ R Calf
    400036: SEAGULL_ R Clavicle
    400038: SEAGULL_ R Foot
    400040: SEAGULL_ R Forearm
    400042: SEAGULL_ R Hand
    400044: SEAGULL_ R HorseLink
    400046: SEAGULL_ R Thigh
    400048: SEAGULL_ R Toe0
    400050: SEAGULL_ R Toe01
    400052: SEAGULL_ R Toe02
    400054: SEAGULL_ R UpperArm
    400056: SEAGULL_ Spine
    400058: SEAGULL_ Tail
    400060: SEAGULL_ TailL
    400062: SEAGULL_ TailR
    7400000: Take 001
    11100000: //RootNode
  materials:
    importMaterials: 1
    materialName: 0
    materialSearch: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    optimizeGameObjects: 0
    animationCompression: 0
    animationRotationError: .5
    animationPositionError: .5
    animationScaleError: .5
    animationWrapMode: 2
    extraExposedTransformPaths: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: .00999999978
    meshCompression: 0
    addColliders: 0
    importBlendShapes: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    weldVertices: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
  tangentSpace:
    normalSmoothAngle: 60
    splitTangentsAcrossUV: 1
    normalImportMode: 0
    tangentImportMode: 1
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    human: []
    skeleton: []
    armTwist: .5
    foreArmTwist: .5
    upperLegTwist: .5
    legTwist: .5
    armStretch: .0500000007
    legStretch: .0500000007
    feetSpacing: 0
    rootMotionBoneName: 
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 1
  additionalBone: 0
  userData: 
