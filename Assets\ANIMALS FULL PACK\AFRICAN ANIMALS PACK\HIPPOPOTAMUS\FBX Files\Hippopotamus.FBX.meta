fileFormatVersion: 2
guid: 5ffb2bcb5dbbf63499bf4c6bfd591bb8
ModelImporter:
  serializedVersion: 20300
  internalIDToNameTable:
  - first:
      1: 100000
    second: //RootNode
  - first:
      1: 100002
    second: HIPPOPOTAMUS_
  - first:
      1: 100004
    second: HIPPOPOTAMUS_ Head
  - first:
      1: 100006
    second: HIPPOPOTAMUS_ L Calf
  - first:
      1: 100008
    second: HIPPOPOTAMUS_ L Clavicle
  - first:
      1: 100010
    second: HIPPOPOTAMUS_ L Finger0
  - first:
      1: 100012
    second: HIPPOPOTAMUS_ L Foot
  - first:
      1: 100014
    second: HIPPOPOTAMUS_ L Forearm
  - first:
      1: 100016
    second: HIPPOPOTAMUS_ L Hand
  - first:
      1: 100018
    second: HIPPOPOTAMUS_ L HorseLink
  - first:
      1: 100020
    second: HIPPOPOTAMUS_ L Thigh
  - first:
      1: 100022
    second: HIPPOPOTAMUS_ L UpperArm
  - first:
      1: 100024
    second: HIPPOPOTAMUS_ Neck
  - first:
      1: 100026
    second: HIPPOPOTAMUS_ Neck1
  - first:
      1: 100028
    second: HIPPOPOTAMUS_ Pelvis
  - first:
      1: 100030
    second: HIPPOPOTAMUS_ Queue de cheval 1
  - first:
      1: 100032
    second: HIPPOPOTAMUS_ R Calf
  - first:
      1: 100034
    second: HIPPOPOTAMUS_ R Clavicle
  - first:
      1: 100036
    second: HIPPOPOTAMUS_ R Finger0
  - first:
      1: 100038
    second: HIPPOPOTAMUS_ R Foot
  - first:
      1: 100040
    second: HIPPOPOTAMUS_ R Forearm
  - first:
      1: 100042
    second: HIPPOPOTAMUS_ R Hand
  - first:
      1: 100044
    second: HIPPOPOTAMUS_ R HorseLink
  - first:
      1: 100046
    second: HIPPOPOTAMUS_ R Thigh
  - first:
      1: 100048
    second: HIPPOPOTAMUS_ R UpperArm
  - first:
      1: 100050
    second: HIPPOPOTAMUS_ Spine
  - first:
      1: 100052
    second: HIPPOPOTAMUS_ Spine1
  - first:
      1: 100054
    second: HIPPOPOTAMUS_ Tail
  - first:
      1: 100056
    second: HIPPOPOTAMUS_ Tail1
  - first:
      1: 100058
    second: HIPPOPOTAMUS_ Tail2
  - first:
      1: 100060
    second: root
  - first:
      1: 100062
    second: SK_Hippopotamus_LOD0
  - first:
      1: 100064
    second: SK_Hippopotamus_LOD1
  - first:
      1: 100066
    second: SK_Hippopotamus_LOD2
  - first:
      1: 100068
    second: HIPPOPOTAMUS_ Footsteps
  - first:
      1: 100070
    second: HIPPOPOTAMUS_ HeadNub
  - first:
      1: 100072
    second: HIPPOPOTAMUS_ L Finger0Nub
  - first:
      1: 100074
    second: HIPPOPOTAMUS_ L Toe0
  - first:
      1: 100076
    second: HIPPOPOTAMUS_ L Toe0Nub
  - first:
      1: 100078
    second: HIPPOPOTAMUS_ Queue de cheval 1Nub
  - first:
      1: 100080
    second: HIPPOPOTAMUS_ R Finger0Nub
  - first:
      1: 100082
    second: HIPPOPOTAMUS_ R Toe0
  - first:
      1: 100084
    second: HIPPOPOTAMUS_ R Toe0Nub
  - first:
      1: 100086
    second: HIPPOPOTAMUS_ TailNub
  - first:
      1: 100088
    second: nub
  - first:
      4: 400000
    second: //RootNode
  - first:
      4: 400002
    second: HIPPOPOTAMUS_
  - first:
      4: 400004
    second: HIPPOPOTAMUS_ Head
  - first:
      4: 400006
    second: HIPPOPOTAMUS_ L Calf
  - first:
      4: 400008
    second: HIPPOPOTAMUS_ L Clavicle
  - first:
      4: 400010
    second: HIPPOPOTAMUS_ L Finger0
  - first:
      4: 400012
    second: HIPPOPOTAMUS_ L Foot
  - first:
      4: 400014
    second: HIPPOPOTAMUS_ L Forearm
  - first:
      4: 400016
    second: HIPPOPOTAMUS_ L Hand
  - first:
      4: 400018
    second: HIPPOPOTAMUS_ L HorseLink
  - first:
      4: 400020
    second: HIPPOPOTAMUS_ L Thigh
  - first:
      4: 400022
    second: HIPPOPOTAMUS_ L UpperArm
  - first:
      4: 400024
    second: HIPPOPOTAMUS_ Neck
  - first:
      4: 400026
    second: HIPPOPOTAMUS_ Neck1
  - first:
      4: 400028
    second: HIPPOPOTAMUS_ Pelvis
  - first:
      4: 400030
    second: HIPPOPOTAMUS_ Queue de cheval 1
  - first:
      4: 400032
    second: HIPPOPOTAMUS_ R Calf
  - first:
      4: 400034
    second: HIPPOPOTAMUS_ R Clavicle
  - first:
      4: 400036
    second: HIPPOPOTAMUS_ R Finger0
  - first:
      4: 400038
    second: HIPPOPOTAMUS_ R Foot
  - first:
      4: 400040
    second: HIPPOPOTAMUS_ R Forearm
  - first:
      4: 400042
    second: HIPPOPOTAMUS_ R Hand
  - first:
      4: 400044
    second: HIPPOPOTAMUS_ R HorseLink
  - first:
      4: 400046
    second: HIPPOPOTAMUS_ R Thigh
  - first:
      4: 400048
    second: HIPPOPOTAMUS_ R UpperArm
  - first:
      4: 400050
    second: HIPPOPOTAMUS_ Spine
  - first:
      4: 400052
    second: HIPPOPOTAMUS_ Spine1
  - first:
      4: 400054
    second: HIPPOPOTAMUS_ Tail
  - first:
      4: 400056
    second: HIPPOPOTAMUS_ Tail1
  - first:
      4: 400058
    second: HIPPOPOTAMUS_ Tail2
  - first:
      4: 400060
    second: root
  - first:
      4: 400062
    second: SK_Hippopotamus_LOD0
  - first:
      4: 400064
    second: SK_Hippopotamus_LOD1
  - first:
      4: 400066
    second: SK_Hippopotamus_LOD2
  - first:
      4: 400068
    second: HIPPOPOTAMUS_ Footsteps
  - first:
      4: 400070
    second: HIPPOPOTAMUS_ HeadNub
  - first:
      4: 400072
    second: HIPPOPOTAMUS_ L Finger0Nub
  - first:
      4: 400074
    second: HIPPOPOTAMUS_ L Toe0
  - first:
      4: 400076
    second: HIPPOPOTAMUS_ L Toe0Nub
  - first:
      4: 400078
    second: HIPPOPOTAMUS_ Queue de cheval 1Nub
  - first:
      4: 400080
    second: HIPPOPOTAMUS_ R Finger0Nub
  - first:
      4: 400082
    second: HIPPOPOTAMUS_ R Toe0
  - first:
      4: 400084
    second: HIPPOPOTAMUS_ R Toe0Nub
  - first:
      4: 400086
    second: HIPPOPOTAMUS_ TailNub
  - first:
      4: 400088
    second: nub
  - first:
      43: 4300000
    second: SK_Hippopotamus_LOD0
  - first:
      43: 4300002
    second: SK_Hippopotamus_LOD2
  - first:
      43: 4300004
    second: SK_Hippopotamus_LOD1
  - first:
      74: 7400000
    second: Take 001
  - first:
      95: 9500000
    second: //RootNode
  - first:
      137: 13700000
    second: SK_Hippopotamus_LOD0
  - first:
      137: 13700002
    second: SK_Hippopotamus_LOD1
  - first:
      137: 13700004
    second: SK_Hippopotamus_LOD2
  - first:
      205: 20500000
    second: //RootNode
  externalObjects: {}
  materials:
    materialImportMode: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 0
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 0
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages:
    - 0.25
    - 0.125
    - 0.01
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 0
    importBlendShapes: 1
    importCameras: 0
    importLights: 0
    fileIdsGeneration: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    meshOptimizationFlags: -1
    indexFormat: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 0
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 4
    normalCalculationMode: 0
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 1
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips:
  - f0d978e6741ec7544802d80adb7d11ae
  - e2586979d1aa21848a18e2507d4a5934
  - 9073d3c6db022384c89849205f90d257
  - 3f55b04f2c19aaa448486af851bbcb60
  - 7b127b9b18e5a004a89dbd9cd63a1bf5
  - 480297d0d1b1670409a280972201e26d
  - 2ef2f6c23af45694db84ec1e09b3a098
  - f80148c5b70000647b1fa04e10e1c26b
  - 50c37ed76be337144b53a1ee30cf4fcd
  - 06da2289e8c0e1946a984ffcdad47867
  - 911406ec94ddefd4d94b6bf4bfbb62e0
  - c91a94f13a7be984bbfad2d2c32afa41
  - 5f6ab4cd5e14f9240b75861b7c7a905a
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: root
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 0
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 2
  humanoidOversampling: 1
  avatarSetup: 1
  addHumanoidExtraRootOnlyWhenUsingAvatar: 0
  remapMaterialsIfMaterialImportModeIsNone: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
