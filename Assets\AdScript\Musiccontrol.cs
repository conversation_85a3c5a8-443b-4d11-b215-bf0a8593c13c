using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class Musiccontrol : MonoBehaviour
{
    public GameObject[] objects; // Renamed for clarity
    public GameObject music;

    // Update is called once per frame
    void Update()
    {
        // Check if any of the GameObjects in the 'objects' array are active
        bool anyActive = false; // Variable to track if any GameObject is active

        foreach (GameObject obj in objects)
        {
            if (obj.activeInHierarchy)
            {
                anyActive = true; // Set true if any object is active
                break; // Exit loop early if we find an active object
            }
        }

        // Set music active/inactive based on the active state of GameObjects
        music.SetActive(!anyActive); // Disable music if any object is active
    }
}
