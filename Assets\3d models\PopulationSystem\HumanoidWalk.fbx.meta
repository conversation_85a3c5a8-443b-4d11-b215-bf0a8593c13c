fileFormatVersion: 2
guid: 4d5bad854e091c04ba3741ea4c8ee24b
ModelImporter:
  serializedVersion: 26
  internalIDToNameTable:
  - first:
      74: 4344548913397313303
    second: _1_a_U1_M_P_WalkForward_NtrlFaceFwd__Fb_p0_No_0_PJ_3
  externalObjects: {}
  materials:
    importMaterials: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: _1_a_U1_M_P_WalkForward_NtrlFaceFwd__Fb_p0_No_0_PJ_3
      takeName: _1_a_U1_M_P_WalkForward_NtrlFaceFwd__Fb_p0_No_0_PJ_3
      internalID: 4344548913397313303
      firstFrame: 0
      lastFrame: 391
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 0
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    meshOptimizationFlags: -1
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 0
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    serializedVersion: 3
    human:
    - boneName: Hips
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftUpLeg
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightUpLeg
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftLeg
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightLeg
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftFoot
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightFoot
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Chest
      humanName: Chest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Neck
      humanName: Neck
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Head
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftShoulder
      humanName: LeftShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightShoulder
      humanName: RightShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftArm
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightArm
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftForeArm
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightForeArm
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHand
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHand
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftToes
      humanName: LeftToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightToes
      humanName: RightToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftEye
      humanName: LeftEye
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightEye
      humanName: RightEye
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Jaw
      humanName: Jaw
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandThumb1
      humanName: Left Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandThumb2
      humanName: Left Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandThumb3
      humanName: Left Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandIndex1
      humanName: Left Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandIndex2
      humanName: Left Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandIndex3
      humanName: Left Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandMiddle1
      humanName: Left Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandMiddle2
      humanName: Left Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandMiddle3
      humanName: Left Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandRing1
      humanName: Left Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandRing2
      humanName: Left Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandRing3
      humanName: Left Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandPinky1
      humanName: Left Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandPinky2
      humanName: Left Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandPinky3
      humanName: Left Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandThumb1
      humanName: Right Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandThumb2
      humanName: Right Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandThumb3
      humanName: Right Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandIndex1
      humanName: Right Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandIndex2
      humanName: Right Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandIndex3
      humanName: Right Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandMiddle1
      humanName: Right Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandMiddle2
      humanName: Right Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandMiddle3
      humanName: Right Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandRing1
      humanName: Right Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandRing2
      humanName: Right Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandRing3
      humanName: Right Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandPinky1
      humanName: Right Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandPinky2
      humanName: Right Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandPinky3
      humanName: Right Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: HumanoidWalk(Clone)
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Reference
      parentName: HumanoidWalk(Clone)
      position: {x: -0, y: 0, z: 0}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Hips
      parentName: Reference
      position: {x: -0.0009061508, y: 0.9556605, z: 0.08152438}
      rotation: {x: 0.005763377, y: 0.0057693576, z: 0.009955815, w: 0.9999172}
      scale: {x: 1, y: 1, z: 1}
    - name: Spine
      parentName: Hips
      position: {x: 2.646978e-25, y: 0.092263184, z: 0.015771331}
      rotation: {x: 0.009514312, y: 0.0010338367, z: -0.01515207, w: 0.9998394}
      scale: {x: 1, y: 1, z: 1}
    - name: Chest
      parentName: Spine
      position: {x: -0, y: 0.16254029, z: -0.0016560555}
      rotation: {x: 0.021924045, y: -0.00029609667, z: 0.004401856, w: 0.9997499}
      scale: {x: 1, y: 1, z: 1}
    - name: Neck
      parentName: Chest
      position: {x: -0, y: 0.2590093, z: -0.032413255}
      rotation: {x: 0.06710078, y: 0.010529975, z: -0.0024828345, w: 0.9976876}
      scale: {x: 1, y: 1, z: 1}
    - name: Head
      parentName: Neck
      position: {x: -2.646978e-25, y: 0.08307038, z: 0.0113267815}
      rotation: {x: -0.020869793, y: 0.008163056, z: 0.0075526037, w: 0.9997204}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftEye
      parentName: Head
      position: {x: -0.020848233, y: 0.0825027, z: 0.055427432}
      rotation: {x: 2.7755576e-17, y: 2.1141942e-17, z: 7.806255e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightEye
      parentName: Head
      position: {x: 0.020849999, y: 0.08250283, z: 0.0554274}
      rotation: {x: 2.7755576e-17, y: 2.1141942e-17, z: 7.806255e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Jaw
      parentName: Head
      position: {x: 1.7347234e-20, y: 0.0111267585, z: 0.010327543}
      rotation: {x: 2.7755576e-17, y: 2.1141942e-17, z: 7.806255e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: TongueBack
      parentName: Jaw
      position: {x: -1.7347234e-20, y: -0.022869369, z: 0.010095409}
      rotation: {x: 3.9496614e-36, y: 2.2768248e-18, z: -1.7347235e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: TongueTip
      parentName: Jaw
      position: {x: -1.7347234e-20, y: -0.023278812, z: 0.03832271}
      rotation: {x: 3.9496614e-36, y: 2.2768248e-18, z: -1.7347235e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftLipLower
      parentName: Jaw
      position: {x: -0.014250817, y: -0.02168876, z: 0.08224063}
      rotation: {x: 3.9496614e-36, y: 2.2768248e-18, z: -1.7347235e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: JawEND
      parentName: Jaw
      position: {x: -1.7347234e-20, y: -0.04828876, z: 0.07185171}
      rotation: {x: -3.9496614e-36, y: 2.2768248e-18, z: -1.7347235e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightLipLower
      parentName: Jaw
      position: {x: 0.014250817, y: -0.02168876, z: 0.082238786}
      rotation: {x: 3.9496614e-36, y: 2.2768248e-18, z: -1.7347235e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightLipCorner
      parentName: Jaw
      position: {x: 0.03284, y: -0.01657876, z: 0.066118784}
      rotation: {x: 3.9496614e-36, y: 2.2768248e-18, z: -1.7347235e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftLipCorner
      parentName: Jaw
      position: {x: -0.032843262, y: -0.01657876, z: 0.066121764}
      rotation: {x: 3.9496614e-36, y: 2.2768248e-18, z: -1.7347235e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftLipUpper
      parentName: Head
      position: {x: -0.014501322, y: -0.005111811, z: 0.09461884}
      rotation: {x: 2.7755576e-17, y: 2.1141942e-17, z: 7.806255e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftNostril
      parentName: Head
      position: {x: -0.0179, y: 0.026312828, z: 0.0908674}
      rotation: {x: -0.034899496, y: 2.0856628e-17, z: 8.539343e-18, w: 0.99939084}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftCheek
      parentName: Head
      position: {x: -0.054244027, y: 0.03370195, z: 0.0594304}
      rotation: {x: 2.7755576e-17, y: 2.1141942e-17, z: 7.806255e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftEyelidLower
      parentName: Head
      position: {x: -0.035618957, y: 0.06507366, z: 0.07623474}
      rotation: {x: 2.7755576e-17, y: 2.1141942e-17, z: 7.806255e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftEyelidUpper
      parentName: Head
      position: {x: -0.034406897, y: 0.10060814, z: 0.08020531}
      rotation: {x: 2.7755576e-17, y: 2.1141942e-17, z: 7.806255e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftInnerBrow
      parentName: Head
      position: {x: -0.012062691, y: 0.118765265, z: 0.093466826}
      rotation: {x: 2.7755576e-17, y: 2.1141942e-17, z: 7.806255e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftIOuterBrow
      parentName: Head
      position: {x: -0.05503987, y: 0.11482529, z: 0.061777398}
      rotation: {x: 2.7755576e-17, y: 2.1141942e-17, z: 7.806255e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightInnerBrow
      parentName: Head
      position: {x: 0.012062687, y: 0.118765265, z: 0.093466826}
      rotation: {x: 2.7755576e-17, y: 2.1141942e-17, z: 7.806255e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightIOuterBrow
      parentName: Head
      position: {x: 0.055040002, y: 0.11482283, z: 0.061777398}
      rotation: {x: -0.034899496, y: 2.1401497e-17, z: 7.063657e-18, w: 0.99939084}
      scale: {x: 1, y: 1, z: 1}
    - name: RightEyelidUpper
      parentName: Head
      position: {x: 0.03441, y: 0.10061283, z: 0.08020739}
      rotation: {x: 2.7755576e-17, y: 2.1141942e-17, z: 7.806255e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightEyelidLower
      parentName: Head
      position: {x: 0.03562, y: 0.06507283, z: 0.0762374}
      rotation: {x: 2.7755576e-17, y: 2.1141942e-17, z: 7.806255e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightCheek
      parentName: Head
      position: {x: 0.054239996, y: 0.033702828, z: 0.0594274}
      rotation: {x: 2.7755576e-17, y: 2.1141942e-17, z: 7.806255e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightNostril
      parentName: Head
      position: {x: 0.0179, y: 0.026308905, z: 0.09087062}
      rotation: {x: 2.7755576e-17, y: 2.1141942e-17, z: 7.806255e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightLipUpper
      parentName: Head
      position: {x: 0.014501322, y: -0.0051071714, z: 0.094617404}
      rotation: {x: 1.3877788e-17, y: 1.8431437e-18, z: -2.1684046e-18, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightShoulder
      parentName: Chest
      position: {x: 0.038286015, y: 0.22162114, z: -0.017063085}
      rotation: {x: 0.14980654, y: 0.986935, z: -0.02117159, w: -0.055398952}
      scale: {x: 1, y: 1, z: 1}
    - name: RightArm
      parentName: RightShoulder
      position: {x: -0.100501455, y: -0.0000024995522, z: -0.000000051557407}
      rotation: {x: 0.12564468, y: 0.98444396, z: 0.06250317, w: 0.10572071}
      scale: {x: 1, y: 1, z: 1}
    - name: RightForeArm
      parentName: RightArm
      position: {x: 0.25342825, y: 0.006011353, z: -0.016704524}
      rotation: {x: 0.26110205, y: 0.018888928, z: -0.030671954, w: 0.96463895}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHand
      parentName: RightForeArm
      position: {x: 0.2453737, y: 0.021641772, z: 0.005550465}
      rotation: {x: -0.0395322, y: -0.00023953961, z: 0.05532989, w: 0.9976852}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandPinky1
      parentName: RightHand
      position: {x: 0.06680334, y: -0.0019941088, z: -0.030756146}
      rotation: {x: -0.2864791, y: -0.21416982, z: 0.021083057, w: 0.9336041}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandPinky2
      parentName: RightHandPinky1
      position: {x: 0.028530842, y: -0.001397143, z: -0.011623796}
      rotation: {x: 0.029357253, y: 0.00056318904, z: -0.06212374, w: 0.99763644}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandPinky3
      parentName: RightHandPinky2
      position: {x: 0.02142686, y: -0.00055350893, z: -0.008516608}
      rotation: {x: -0.04629117, y: 0.00056925585, z: -0.24186173, w: 0.96920574}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandRing1
      parentName: RightHand
      position: {x: 0.070598476, y: 0.0024570965, z: -0.009821458}
      rotation: {x: -0.10453742, y: -0.10143086, z: -0.025803326, w: 0.9889984}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandRing2
      parentName: RightHandRing1
      position: {x: 0.042887185, y: -0.0013753821, z: -0.004945858}
      rotation: {x: 0.020977879, y: -0.021641867, z: 0.07535293, w: 0.99670136}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandRing3
      parentName: RightHandRing2
      position: {x: 0.029500604, y: -0.0076929354, z: -0.004622256}
      rotation: {x: -0.018509768, y: 0.0023600159, z: -0.24554446, w: 0.96920574}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandMiddle1
      parentName: RightHand
      position: {x: 0.075647645, y: 0.0047914027, z: 0.011853182}
      rotation: {x: -0.092045605, y: 0.02068401, z: -0.0604925, w: 0.9937004}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandMiddle2
      parentName: RightHandMiddle1
      position: {x: 0.043809064, y: 0.00019418815, z: 0.006454936}
      rotation: {x: -0.016960142, y: -0.04367101, z: 0.08092607, w: 0.9956185}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandMiddle3
      parentName: RightHandMiddle2
      position: {x: 0.03307247, y: -0.007547537, z: 0.0016898462}
      rotation: {x: 0.0072286935, y: -0.0008138619, z: -0.29040176, w: 0.9568772}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandIndex1
      parentName: RightHand
      position: {x: 0.0747695, y: -0.0012430536, z: 0.034344498}
      rotation: {x: 0.06961239, y: 0.11270627, z: -0.010377135, w: 0.99113256}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandIndex2
      parentName: RightHandIndex1
      position: {x: 0.0370584, y: 0.00072612107, z: 0.014538894}
      rotation: {x: -0.068797775, y: 0.021604061, z: 0.042185087, w: 0.99650425}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandIndex3
      parentName: RightHandIndex2
      position: {x: 0.025225038, y: -0.0049664653, z: 0.011012146}
      rotation: {x: 0.008972452, y: 0.012900089, z: -0.18306519, w: 0.9829752}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandThumb1
      parentName: RightHand
      position: {x: 0.014684916, y: -0.011104942, z: 0.025858095}
      rotation: {x: -0.09963443, y: 0.02329436, z: 0.1310213, w: 0.98608506}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandThumb2
      parentName: RightHandThumb1
      position: {x: 0.016374, y: -0.00529, z: 0.02349136}
      rotation: {x: -0.029242907, y: -0.09792916, z: -0.007218063, w: 0.99473745}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandThumb3
      parentName: RightHandThumb2
      position: {x: 0.02546, y: -0.00764, z: 0.020833}
      rotation: {x: 0.04826964, y: 0.21970885, z: 0.019951679, w: 0.97416633}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftShoulder
      parentName: Chest
      position: {x: -0.038285997, y: 0.2216225, z: -0.017063085}
      rotation: {x: -0.030235812, y: -0.08046522, z: 0.14568214, w: 0.9855901}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftArm
      parentName: LeftShoulder
      position: {x: -0.10050205, y: 5.684342e-16, z: -3.330669e-18}
      rotation: {x: 0.00805067, y: 0.07593758, z: -0.13212626, w: 0.9882871}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftForeArm
      parentName: LeftArm
      position: {x: -0.2540493, y: 5.684342e-16, z: 1.11022296e-17}
      rotation: {x: 0.27812704, y: 0.03635519, z: -0.015607668, w: 0.9597292}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHand
      parentName: LeftForeArm
      position: {x: -0.24638927, y: 0, z: -1.9984014e-16}
      rotation: {x: -0.0058455463, y: -0.07191072, z: -0.0030291397, w: 0.9973894}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandPinky1
      parentName: LeftHand
      position: {x: -0.06565995, y: -0.007825106, z: -0.032251246}
      rotation: {x: -0.28387687, y: 0.03656768, z: 0.08766472, w: 0.9541444}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandPinky2
      parentName: LeftHandPinky1
      position: {x: -0.030805448, y: -0.000030874573, z: -0.0014480775}
      rotation: {x: 0.04704805, y: -0.021199852, z: 0.03749731, w: 0.9979635}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandPinky3
      parentName: LeftHandPinky2
      position: {x: -0.023064027, y: -0.0000064025808, z: 0.000000018332095}
      rotation: {x: 0.046291143, y: -0.0005692553, z: 0.24186173, w: 0.96920574}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandRing1
      parentName: LeftHand
      position: {x: -0.07030211, y: -0.0037453093, z: -0.011411792}
      rotation: {x: -0.10562233, y: 0.056129213, z: 0.08703167, w: 0.988999}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandRing2
      parentName: LeftHandRing1
      position: {x: -0.043135457, y: -0.000020882308, z: -0.0022351784}
      rotation: {x: 0.018425977, y: -0.025610533, z: 0.033297546, w: 0.99894744}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandRing3
      parentName: LeftHandRing2
      position: {x: -0.030835565, y: 7.7103546e-11, z: -0.00000001649327}
      rotation: {x: 0.018509813, y: -0.0023600212, z: 0.24554446, w: 0.96920574}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandMiddle1
      parentName: LeftHand
      position: {x: -0.076023825, y: -0.0018851344, z: 0.010141229}
      rotation: {x: -0.09939502, y: 0.041070748, z: 0.09351162, w: 0.9897926}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandMiddle2
      parentName: LeftHandMiddle1
      position: {x: -0.044280436, y: 0.000004798874, z: -0.00042540013}
      rotation: {x: -0.0124357985, y: -0.0076591205, z: 0.031809237, w: 0.99938726}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandMiddle3
      parentName: LeftHandMiddle2
      position: {x: -0.033964828, y: -0.000000012197929, z: 0.0000000037564827}
      rotation: {x: -0.007228648, y: 0.00081385655, z: 0.29040176, w: 0.9568772}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandIndex1
      parentName: LeftHand
      position: {x: -0.0751258, y: -0.0078414045, z: 0.032652643}
      rotation: {x: 0.06495644, y: 0.05091051, z: 0.058088724, w: 0.9948942}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandIndex2
      parentName: LeftHandIndex1
      position: {x: -0.03979728, y: 0.000049808405, z: 0.0011857504}
      rotation: {x: -0.0673712, y: 0.015347427, z: 0.033307113, w: 0.99705374}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandIndex3
      parentName: LeftHandIndex2
      position: {x: -0.027968477, y: -0.000000006281225, z: -0.00000005171866}
      rotation: {x: -0.06627773, y: -0.0075497325, z: 0.17120177, w: 0.9829752}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandThumb1
      parentName: LeftHand
      position: {x: -0.014231241, y: -0.012377825, z: 0.025531668}
      rotation: {x: -0.15654075, y: -0.06274938, z: -0.14152853, w: 0.9754626}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandThumb2
      parentName: LeftHandThumb1
      position: {x: -0.016374, y: -0.00529, z: 0.023491409}
      rotation: {x: -0.029214446, y: 0.09793903, z: 0.007191879, w: 0.9947375}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandThumb3
      parentName: LeftHandThumb2
      position: {x: -0.02546, y: -0.00764, z: 0.020833}
      rotation: {x: 0.04827262, y: -0.21973689, z: -0.019962844, w: 0.9741596}
      scale: {x: 1, y: 1, z: 1}
    - name: RightUpLeg
      parentName: Hips
      position: {x: 0.075449534, y: -0.04566399, z: 0}
      rotation: {x: -0.03376921, y: 0.016441053, z: -0.0035962004, w: 0.99928796}
      scale: {x: 1, y: 1, z: 1}
    - name: RightLeg
      parentName: RightUpLeg
      position: {x: 0.020550467, y: -0.40913, z: -0.00071864796}
      rotation: {x: 0.09446614, y: 0.049148384, z: 0.013181649, w: 0.99422675}
      scale: {x: 1, y: 1, z: 1}
    - name: RightFoot
      parentName: RightLeg
      position: {x: 0.0051529994, y: -0.4231559, z: -0.027648851}
      rotation: {x: -0.0655321, y: 0.01000078, z: 0.004351351, w: 0.9977909}
      scale: {x: 1, y: 1, z: 1}
    - name: RightToes
      parentName: RightFoot
      position: {x: 0.007487, y: -0.0731673, z: 0.1454275}
      rotation: {x: 0.019415032, y: 0.004927584, z: -0.0066799005, w: 0.9997771}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftUpLeg
      parentName: Hips
      position: {x: -0.0754495, y: -0.04566402, z: 0}
      rotation: {x: -0.04491383, y: -0.05108902, z: -0.006209079, w: 0.99766433}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftLeg
      parentName: LeftUpLeg
      position: {x: -0.020550499, y: -0.40912998, z: -0.00071864796}
      rotation: {x: 0.0968651, y: -0.014973701, z: -0.009664302, w: 0.99513793}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftFoot
      parentName: LeftLeg
      position: {x: -0.0051529994, y: -0.4231559, z: -0.027648851}
      rotation: {x: -0.053543165, y: -0.0013233997, z: 0.00091903226, w: 0.99856424}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftToes
      parentName: LeftFoot
      position: {x: -0.007487, y: -0.0731673, z: 0.14542712}
      rotation: {x: 0.008492801, y: -0.017236067, z: 0.0063598882, w: 0.99979514}
      scale: {x: 1, y: 1, z: 1}
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 1
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 3
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
