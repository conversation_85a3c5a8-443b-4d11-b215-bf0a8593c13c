fileFormatVersion: 2
guid: 8e45344d7827cf840a203a1dfc78f01f
ModelImporter:
  serializedVersion: 20300
  internalIDToNameTable:
  - first:
      1: 100000
    second: //RootNode
  - first:
      1: 100002
    second: DUCK
  - first:
      1: 100004
    second: DUCK_
  - first:
      1: 100006
    second: DUC<PERSON>_ Head
  - first:
      1: 100008
    second: DUCK_ L Calf
  - first:
      1: 100010
    second: DUCK_ L Clavicle
  - first:
      1: 100012
    second: DUCK_ L Foot
  - first:
      1: 100014
    second: DUCK_ L Forearm
  - first:
      1: 100016
    second: DUCK_ L Hand
  - first:
      1: 100018
    second: DUCK_ L HorseLink
  - first:
      1: 100020
    second: DUCK_ L Thigh
  - first:
      1: 100022
    second: DUCK_ L Toe0
  - first:
      1: 100024
    second: DUCK_ L Toe01
  - first:
      1: 100026
    second: DUCK_ L Toe02
  - first:
      1: 100028
    second: DUCK_ L UpperArm
  - first:
      1: 100030
    second: DUCK_ Neck
  - first:
      1: 100032
    second: DUCK_ Neck1
  - first:
      1: 100034
    second: DUCK_ Neck2
  - first:
      1: 100036
    second: <PERSON><PERSON><PERSON>_ Pelvis
  - first:
      1: 100038
    second: DUCK_ Queue de cheval 1
  - first:
      1: 100040
    second: DUCK_ R Calf
  - first:
      1: 100042
    second: DUCK_ R Clavicle
  - first:
      1: 100044
    second: DUCK_ R Foot
  - first:
      1: 100046
    second: DUCK_ R Forearm
  - first:
      1: 100048
    second: DUCK_ R Hand
  - first:
      1: 100050
    second: DUCK_ R HorseLink
  - first:
      1: 100052
    second: DUCK_ R Thigh
  - first:
      1: 100054
    second: DUCK_ R Toe0
  - first:
      1: 100056
    second: DUCK_ R Toe01
  - first:
      1: 100058
    second: DUCK_ R Toe02
  - first:
      1: 100060
    second: DUCK_ R UpperArm
  - first:
      1: 100062
    second: DUCK_ Spine
  - first:
      1: 100064
    second: DUCK_ Tail
  - first:
      1: 100066
    second: DUCK_ TailL
  - first:
      1: 100068
    second: DUCK_ TailR
  - first:
      4: 400000
    second: //RootNode
  - first:
      4: 400002
    second: DUCK
  - first:
      4: 400004
    second: DUCK_
  - first:
      4: 400006
    second: DUCK_ Head
  - first:
      4: 400008
    second: DUCK_ L Calf
  - first:
      4: 400010
    second: DUCK_ L Clavicle
  - first:
      4: 400012
    second: DUCK_ L Foot
  - first:
      4: 400014
    second: DUCK_ L Forearm
  - first:
      4: 400016
    second: DUCK_ L Hand
  - first:
      4: 400018
    second: DUCK_ L HorseLink
  - first:
      4: 400020
    second: DUCK_ L Thigh
  - first:
      4: 400022
    second: DUCK_ L Toe0
  - first:
      4: 400024
    second: DUCK_ L Toe01
  - first:
      4: 400026
    second: DUCK_ L Toe02
  - first:
      4: 400028
    second: DUCK_ L UpperArm
  - first:
      4: 400030
    second: DUCK_ Neck
  - first:
      4: 400032
    second: DUCK_ Neck1
  - first:
      4: 400034
    second: DUCK_ Neck2
  - first:
      4: 400036
    second: DUCK_ Pelvis
  - first:
      4: 400038
    second: DUCK_ Queue de cheval 1
  - first:
      4: 400040
    second: DUCK_ R Calf
  - first:
      4: 400042
    second: DUCK_ R Clavicle
  - first:
      4: 400044
    second: DUCK_ R Foot
  - first:
      4: 400046
    second: DUCK_ R Forearm
  - first:
      4: 400048
    second: DUCK_ R Hand
  - first:
      4: 400050
    second: DUCK_ R HorseLink
  - first:
      4: 400052
    second: DUCK_ R Thigh
  - first:
      4: 400054
    second: DUCK_ R Toe0
  - first:
      4: 400056
    second: DUCK_ R Toe01
  - first:
      4: 400058
    second: DUCK_ R Toe02
  - first:
      4: 400060
    second: DUCK_ R UpperArm
  - first:
      4: 400062
    second: DUCK_ Spine
  - first:
      4: 400064
    second: DUCK_ Tail
  - first:
      4: 400066
    second: DUCK_ TailL
  - first:
      4: 400068
    second: DUCK_ TailR
  - first:
      43: 4300000
    second: DUCK
  - first:
      74: 7400000
    second: Take 001
  - first:
      111: 11100000
    second: //RootNode
  - first:
      137: 13700000
    second: DUCK
  externalObjects: {}
  materials:
    materialImportMode: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 0
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 0.01
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 0
    importBlendShapes: 1
    importCameras: 0
    importLights: 0
    fileIdsGeneration: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    meshOptimizationFlags: -1
    indexFormat: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 0
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 4
    normalCalculationMode: 0
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 1
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips:
  - 7ea0f25fa8103cb4390e982fbc2e7a0f
  - 2b665d52ffd6d84459dfc154596e245c
  - cae42d86d7ce3e245849345ef3e9d37a
  - 87c1bc94ef6d51544a2da73c65d192ee
  - d0fc439f4738a164089476079c95b86f
  - 1254ee6bba45f85418977520676bc015
  - f69c5d1880ccf7d4f922911914b4b7ca
  - 1c2841b802293f0429fbd2c777b57ce3
  - 75098065b95638341aca9fe9b05f8b58
  - 1d0742ad4a860f8488995ec324231197
  - ee08f39e9bd44b54d9ac1e67efaf9b85
  - 0461a3cf88684b848b61d0c82b7854ae
  - d2d092cce4fb56841a929a07315a518f
  - 56a0d7454d0c00545afb33230470dcb4
  - 177a0886b0570754ba7b438f2aaff5ed
  - 49f0fc7a5a94aca4f9e066f36a1deb0c
  - ca9a406a5502d004f93f7bd72355cbdc
  - c761079671b6b334d84faa702b34f709
  - dcee14b99844dcd4a96bccbbc6c100c9
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 0.01
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 1
    skeletonHasParents: 0
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 2
  humanoidOversampling: 1
  avatarSetup: 0
  addHumanoidExtraRootOnlyWhenUsingAvatar: 0
  remapMaterialsIfMaterialImportModeIsNone: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
