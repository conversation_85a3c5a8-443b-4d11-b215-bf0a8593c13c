%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &100140
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 445826}
  m_Layer: 0
  m_Name: RHINOCEROS_ L Clavicle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &445826
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 100140}
  m_LocalRotation: {x: 0.70820487, y: -0.25158745, z: -0.58485264, w: 0.30511805}
  m_LocalPosition: {x: -0.6963283, y: -0.15458938, z: 0.14766951}
  m_LocalScale: {x: 0.99999994, y: 0.99999994, z: 1}
  m_Children:
  - {fileID: 432652}
  m_Father: {fileID: 415612}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &100422
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 432652}
  m_Layer: 0
  m_Name: RHINOCEROS_ L UpperArm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &432652
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 100422}
  m_LocalRotation: {x: -0.5713251, y: -0.5030293, z: 0.11491363, w: 0.6382351}
  m_LocalPosition: {x: -0.378026, y: -0.000000076293944, z: 0}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1.0000001}
  m_Children:
  - {fileID: 408390}
  m_Father: {fileID: 445826}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &101398
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 489770}
  m_Layer: 0
  m_Name: RHINOCEROS_ Tail2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &489770
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 101398}
  m_LocalRotation: {x: 3.5566905e-13, y: -0.00000014528268, z: 0.05238052, w: 0.9986272}
  m_LocalPosition: {x: -0.124336466, y: -0.0001095581, z: -3.0442607e-10}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_Children:
  - {fileID: 418348}
  m_Father: {fileID: 435628}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &105326
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 457112}
  m_Layer: 0
  m_Name: RHINOCEROS_ L Thigh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &457112
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 105326}
  m_LocalRotation: {x: 0.7358285, y: -0.67345047, z: -0.015456787, w: 0.06915157}
  m_LocalPosition: {x: 0.0000003814697, y: 0.00000045776366, z: 0.3210737}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1}
  m_Children:
  - {fileID: 438190}
  m_Father: {fileID: 420560}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &106542
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 475908}
  m_Layer: 0
  m_Name: root
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &475908
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 106542}
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 465710}
  m_Father: {fileID: 491014}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &110410
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 435628}
  m_Layer: 0
  m_Name: RHINOCEROS_ Tail1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &435628
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 110410}
  m_LocalRotation: {x: -2.4998054e-13, y: -0.00000023644559, z: 0.08524861, w: 0.99635977}
  m_LocalPosition: {x: -0.10077545, y: -0.00009735107, z: -2.7008354e-10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 489770}
  m_Father: {fileID: 445686}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &116436
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 435176}
  m_Layer: 0
  m_Name: RHINOCEROS_ R HorseLink
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &435176
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 116436}
  m_LocalRotation: {x: 0.0000000028093374, y: -0.000000005260177, z: -0.39449623, w: 0.91889757}
  m_LocalPosition: {x: -0.478479, y: 0, z: -0.000000019073486}
  m_LocalScale: {x: 0.9999998, y: 0.9999999, z: 0.99999994}
  m_Children:
  - {fileID: 402746}
  m_Father: {fileID: 431506}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &117530
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 465710}
  m_Layer: 0
  m_Name: RHINOCEROS_
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &465710
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 117530}
  m_LocalRotation: {x: 0.50000036, y: -0.4999997, z: 0.4999997, w: 0.50000036}
  m_LocalPosition: {x: -0, y: 1.0491885, z: 1.497}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 420560}
  m_Father: {fileID: 475908}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &117964
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 444120}
  m_Layer: 0
  m_Name: RHINOCEROS_ R Forearm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &444120
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 117964}
  m_LocalRotation: {x: 0.000000002854512, y: -0.000000022173605, z: 0.14831166, w: 0.98894066}
  m_LocalPosition: {x: -0.71698403, y: 0.00000015258789, z: 0.000000019073486}
  m_LocalScale: {x: 1.0000002, y: 0.99999994, z: 1}
  m_Children:
  - {fileID: 426376}
  m_Father: {fileID: 418260}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &118792
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 418348}
  m_Layer: 0
  m_Name: RHINOCEROS_ Tail3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &418348
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 118792}
  m_LocalRotation: {x: -2.2713426e-13, y: -0.000000024450902, z: 0.008815647, w: 0.99996114}
  m_LocalPosition: {x: -0.13848723, y: -0.00009857178, z: -2.7299393e-10}
  m_LocalScale: {x: 0.9999999, y: 0.9999999, z: 1}
  m_Children:
  - {fileID: 404522}
  m_Father: {fileID: 489770}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &121188
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 420560}
  m_Layer: 0
  m_Name: RHINOCEROS_ Pelvis
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &420560
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 121188}
  m_LocalRotation: {x: -0.5, y: 0.5, z: 0.4999993, w: 0.5000007}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 457112}
  - {fileID: 477872}
  - {fileID: 462798}
  - {fileID: 445686}
  m_Father: {fileID: 465710}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &122650
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 402746}
  m_Layer: 0
  m_Name: RHINOCEROS_ R Foot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &402746
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 122650}
  m_LocalRotation: {x: -0.06279548, y: 0.032825593, z: 0.12741184, w: 0.9893156}
  m_LocalPosition: {x: -0.27068278, y: 0, z: -0.000000057220458}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 435176}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &131146
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 496760}
  m_Layer: 0
  m_Name: RHINOCEROS_ L Finger0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &496760
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 131146}
  m_LocalRotation: {x: 0.0005332227, y: 0.017039273, z: -0.054471064, w: 0.9983698}
  m_LocalPosition: {x: -0.19634135, y: -0.014610977, z: -0.0008666992}
  m_LocalScale: {x: 0.99999994, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 415892}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &132748
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 416576}
  m_Layer: 0
  m_Name: RHINOCEROS_ Neck
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &416576
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 132748}
  m_LocalRotation: {x: 2.163033e-14, y: 0.00000012098275, z: -0.043619394, w: 0.99904823}
  m_LocalPosition: {x: -0.8526626, y: 0.19439414, z: 0.00000028302455}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 453786}
  m_Father: {fileID: 415612}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &133150
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 418260}
  m_Layer: 0
  m_Name: RHINOCEROS_ R UpperArm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &418260
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 133150}
  m_LocalRotation: {x: 0.5713096, y: 0.50304705, z: 0.114894204, w: 0.63823843}
  m_LocalPosition: {x: -0.37802592, y: 0.000000076293944, z: -0.000000076293944}
  m_LocalScale: {x: 1.0000001, y: 1.0000002, z: 1}
  m_Children:
  - {fileID: 444120}
  m_Father: {fileID: 499580}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &134470
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 481414}
  m_Layer: 0
  m_Name: RHINOCEROS_ R Finger0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &481414
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 134470}
  m_LocalRotation: {x: -0.00053321937, y: -0.01703927, z: -0.054471064, w: 0.9983698}
  m_LocalPosition: {x: -0.19634135, y: -0.014610977, z: 0.0008666801}
  m_LocalScale: {x: 0.9999999, y: 0.99999994, z: 0.9999999}
  m_Children: []
  m_Father: {fileID: 426376}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &134828
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 431506}
  m_Layer: 0
  m_Name: RHINOCEROS_ R Calf
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &431506
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 134828}
  m_LocalRotation: {x: 0.0000000017671181, y: 5.8884686e-10, z: 0.31613475, w: 0.9487143}
  m_LocalPosition: {x: -0.6457041, y: 0.000000076293944, z: 0.000000019073486}
  m_LocalScale: {x: 0.99999994, y: 0.99999994, z: 0.99999994}
  m_Children:
  - {fileID: 435176}
  m_Father: {fileID: 477872}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &135790
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 462798}
  m_Layer: 0
  m_Name: RHINOCEROS_ Spine
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &462798
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 135790}
  m_LocalRotation: {x: -0.0000020804762, y: 0.000000693676, z: -0.00039815903, w: 0.99999994}
  m_LocalPosition: {x: -0.39750692, y: -0.0006924438, z: 0.0000005522228}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 415612}
  m_Father: {fileID: 420560}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &142702
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 415892}
  m_Layer: 0
  m_Name: RHINOCEROS_ L Hand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &415892
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 142702}
  m_LocalRotation: {x: -0.025053948, y: 0.072440535, z: 0.07004997, w: 0.9945943}
  m_LocalPosition: {x: -0.400742, y: -0.000000076293944, z: 0.000000019073486}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1}
  m_Children:
  - {fileID: 496760}
  m_Father: {fileID: 408390}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &143000
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 403062}
  - component: {fileID: 13700286}
  m_Layer: 0
  m_Name: Sk_Rhinoceros_LOD0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &403062
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 143000}
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 491014}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &13700286
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 143000}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 227fd26d61f22654282b11df95e9762c, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 4
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 4300000, guid: 13a52ff2ed9b95a4e9b0243f68f23ef3, type: 3}
  m_Bones:
  - {fileID: 435838}
  - {fileID: 438190}
  - {fileID: 445826}
  - {fileID: 496760}
  - {fileID: 432280}
  - {fileID: 408390}
  - {fileID: 415892}
  - {fileID: 490260}
  - {fileID: 457112}
  - {fileID: 432652}
  - {fileID: 416576}
  - {fileID: 453786}
  - {fileID: 420560}
  - {fileID: 493298}
  - {fileID: 431506}
  - {fileID: 499580}
  - {fileID: 481414}
  - {fileID: 402746}
  - {fileID: 444120}
  - {fileID: 418260}
  - {fileID: 462798}
  - {fileID: 415612}
  - {fileID: 477872}
  - {fileID: 445686}
  - {fileID: 435628}
  - {fileID: 489770}
  - {fileID: 426376}
  - {fileID: 435176}
  - {fileID: 418348}
  - {fileID: 404522}
  - {fileID: 402524}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 420560}
  m_AABB:
    m_Center: {x: -1.6495403, y: 0.30684388, z: -0.15274656}
    m_Extent: {x: 2.6381633, y: 1.476358, z: 1.307219}
  m_DirtyAABB: 0
--- !u!1 &143700
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 475158}
  - component: {fileID: 13704100}
  m_Layer: 0
  m_Name: Sk_Rhinoceros_LOD2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &475158
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 143700}
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 491014}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &13704100
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 143700}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 227fd26d61f22654282b11df95e9762c, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 4
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 4300002, guid: 13a52ff2ed9b95a4e9b0243f68f23ef3, type: 3}
  m_Bones:
  - {fileID: 435838}
  - {fileID: 438190}
  - {fileID: 445826}
  - {fileID: 496760}
  - {fileID: 432280}
  - {fileID: 408390}
  - {fileID: 415892}
  - {fileID: 490260}
  - {fileID: 457112}
  - {fileID: 432652}
  - {fileID: 416576}
  - {fileID: 453786}
  - {fileID: 420560}
  - {fileID: 493298}
  - {fileID: 431506}
  - {fileID: 499580}
  - {fileID: 481414}
  - {fileID: 402746}
  - {fileID: 444120}
  - {fileID: 418260}
  - {fileID: 462798}
  - {fileID: 415612}
  - {fileID: 477872}
  - {fileID: 445686}
  - {fileID: 435628}
  - {fileID: 489770}
  - {fileID: 435176}
  - {fileID: 426376}
  - {fileID: 418348}
  - {fileID: 404522}
  - {fileID: 402524}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 420560}
  m_AABB:
    m_Center: {x: -1.6484936, y: 0.2795416, z: -0.0013185143}
    m_Extent: {x: 2.6371167, y: 1.3708946, z: 1.1006308}
  m_DirtyAABB: 0
--- !u!1 &149328
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 408390}
  m_Layer: 0
  m_Name: RHINOCEROS_ L Forearm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &408390
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 149328}
  m_LocalRotation: {x: -0.000000006076822, y: 0.000000021690525, z: 0.14825377, w: 0.98894936}
  m_LocalPosition: {x: -0.7169841, y: 0.000000076293944, z: 0.000000019073486}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1}
  m_Children:
  - {fileID: 415892}
  m_Father: {fileID: 432652}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &156606
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 453786}
  m_Layer: 0
  m_Name: RHINOCEROS_ Neck1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &453786
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 156606}
  m_LocalRotation: {x: 1.904718e-13, y: 0.00000012098272, z: -0.043619383, w: 0.99904823}
  m_LocalPosition: {x: -0.26978117, y: -0.00021392822, z: -5.934271e-10}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_Children:
  - {fileID: 435838}
  m_Father: {fileID: 416576}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &167066
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 426376}
  m_Layer: 0
  m_Name: RHINOCEROS_ R Hand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &426376
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 167066}
  m_LocalRotation: {x: 0.025051994, y: -0.07243901, z: 0.0700217, w: 0.99459636}
  m_LocalPosition: {x: -0.40074193, y: 0, z: 0}
  m_LocalScale: {x: 0.99999994, y: 1, z: 1}
  m_Children:
  - {fileID: 481414}
  m_Father: {fileID: 444120}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &171036
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 445686}
  m_Layer: 0
  m_Name: RHINOCEROS_ Tail
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &445686
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 171036}
  m_LocalRotation: {x: 0.0000012537814, y: -0.00000086129387, z: 0.959115, w: -0.28301653}
  m_LocalPosition: {x: 0.43539283, y: -0.009214325, z: -0.0000009792038}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 435628}
  m_Father: {fileID: 420560}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &175008
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 477872}
  m_Layer: 0
  m_Name: RHINOCEROS_ R Thigh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &477872
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 175008}
  m_LocalRotation: {x: 0.7358165, y: -0.67346376, z: 0.015453811, w: -0.06915085}
  m_LocalPosition: {x: -0.0000005340576, y: -0.00000045776366, z: -0.3210737}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 431506}
  m_Father: {fileID: 420560}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &175736
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 472992}
  - component: {fileID: 13762190}
  m_Layer: 0
  m_Name: Sk_Rhinoceros_LOD1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &472992
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 175736}
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 491014}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &13762190
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 175736}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 227fd26d61f22654282b11df95e9762c, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 4
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 4300004, guid: 13a52ff2ed9b95a4e9b0243f68f23ef3, type: 3}
  m_Bones:
  - {fileID: 435838}
  - {fileID: 438190}
  - {fileID: 445826}
  - {fileID: 496760}
  - {fileID: 432280}
  - {fileID: 408390}
  - {fileID: 415892}
  - {fileID: 490260}
  - {fileID: 457112}
  - {fileID: 432652}
  - {fileID: 416576}
  - {fileID: 453786}
  - {fileID: 420560}
  - {fileID: 493298}
  - {fileID: 431506}
  - {fileID: 499580}
  - {fileID: 481414}
  - {fileID: 402746}
  - {fileID: 444120}
  - {fileID: 418260}
  - {fileID: 462798}
  - {fileID: 415612}
  - {fileID: 477872}
  - {fileID: 445686}
  - {fileID: 435628}
  - {fileID: 489770}
  - {fileID: 435176}
  - {fileID: 426376}
  - {fileID: 418348}
  - {fileID: 404522}
  - {fileID: 402524}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 420560}
  m_AABB:
    m_Center: {x: -1.6484936, y: 0.30684394, z: -0.14650679}
    m_Extent: {x: 2.6371167, y: 1.4763579, z: 1.2954311}
  m_DirtyAABB: 0
--- !u!1 &175934
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 432280}
  m_Layer: 0
  m_Name: RHINOCEROS_ L Foot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &432280
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 175934}
  m_LocalRotation: {x: 0.06279624, y: -0.03282303, z: 0.12741245, w: 0.9893156}
  m_LocalPosition: {x: -0.2706828, y: 0.000000076293944, z: 0.000000019073486}
  m_LocalScale: {x: 0.9999999, y: 1, z: 0.99999994}
  m_Children: []
  m_Father: {fileID: 490260}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &178762
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 499580}
  m_Layer: 0
  m_Name: RHINOCEROS_ R Clavicle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &499580
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 178762}
  m_LocalRotation: {x: 0.70820403, y: -0.2515891, z: 0.5848519, w: -0.30512002}
  m_LocalPosition: {x: -0.6963283, y: -0.15458846, z: -0.14767034}
  m_LocalScale: {x: 1, y: 0.99999994, z: 1}
  m_Children:
  - {fileID: 418260}
  m_Father: {fileID: 415612}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &182512
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 404522}
  m_Layer: 0
  m_Name: RHINOCEROS_ Tail4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &404522
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 182512}
  m_LocalRotation: {x: 4.6268514e-14, y: 0.000000096592814, z: -0.034825843, w: 0.9993934}
  m_LocalPosition: {x: -0.123738706, y: -0.000094909665, z: -2.6309863e-10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 402524}
  m_Father: {fileID: 418348}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &183010
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 415612}
  m_Layer: 0
  m_Name: RHINOCEROS_ Spine1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &415612
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 183010}
  m_LocalRotation: {x: 2.163033e-14, y: -0.00000012098275, z: 0.043619394, w: 0.99904823}
  m_LocalPosition: {x: -0.8694532, y: -0.00068954466, z: -0.0000000019128492}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 445826}
  - {fileID: 416576}
  - {fileID: 499580}
  m_Father: {fileID: 462798}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &187734
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 493298}
  m_Layer: 0
  m_Name: RHINOCEROS_ Queue de cheval 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &493298
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 187734}
  m_LocalRotation: {x: 1.2566133e-12, y: 0.0000017762535, z: -0.64041495, w: 0.7680291}
  m_LocalPosition: {x: 0.16581771, y: 0.29339346, z: 0.00000035852207}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 435838}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &189088
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 438190}
  m_Layer: 0
  m_Name: RHINOCEROS_ L Calf
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &438190
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 189088}
  m_LocalRotation: {x: -5.8857913e-10, y: 0.000000007657298, z: 0.3161731, w: 0.94870156}
  m_LocalPosition: {x: -0.64570385, y: -0.000000076293944, z: 0}
  m_LocalScale: {x: 0.99999976, y: 0.9999999, z: 0.99999994}
  m_Children:
  - {fileID: 490260}
  m_Father: {fileID: 457112}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &189316
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 490260}
  m_Layer: 0
  m_Name: RHINOCEROS_ L HorseLink
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &490260
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 189316}
  m_LocalRotation: {x: -0.000000011869238, y: -0.0000000111205445, z: -0.39451736, w: 0.9188885}
  m_LocalPosition: {x: -0.4784789, y: 0.000000076293944, z: 0.000000019073486}
  m_LocalScale: {x: 1, y: 0.9999999, z: 0.99999994}
  m_Children:
  - {fileID: 432280}
  m_Father: {fileID: 438190}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &194574
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 491014}
  - component: {fileID: 9582092}
  - component: {fileID: 20592708}
  m_Layer: 0
  m_Name: Rhinoceros_PBR
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &491014
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 194574}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 475908}
  - {fileID: 403062}
  - {fileID: 472992}
  - {fileID: 475158}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!95 &9582092
Animator:
  serializedVersion: 3
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 194574}
  m_Enabled: 1
  m_Avatar: {fileID: 9000000, guid: 13a52ff2ed9b95a4e9b0243f68f23ef3, type: 3}
  m_Controller: {fileID: 0}
  m_CullingMode: 1
  m_UpdateMode: 0
  m_ApplyRootMotion: 1
  m_LinearVelocityBlending: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorControllerStateOnDisable: 0
--- !u!205 &20592708
LODGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 194574}
  serializedVersion: 2
  m_LocalReferencePoint: {x: 0.15274596, y: 1.1901535, z: 0.6003516}
  m_Size: 5.2763343
  m_FadeMode: 0
  m_AnimateCrossFading: 0
  m_LastLODIsBillboard: 0
  m_LODs:
  - screenRelativeHeight: 0.7894107
    fadeTransitionWidth: 0
    renderers:
    - renderer: {fileID: 13700286}
  - screenRelativeHeight: 0.37513182
    fadeTransitionWidth: 0
    renderers:
    - renderer: {fileID: 13762190}
  - screenRelativeHeight: 0.047997158
    fadeTransitionWidth: 0
    renderers:
    - renderer: {fileID: 13704100}
  m_Enabled: 1
--- !u!1 &195096
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 402524}
  m_Layer: 0
  m_Name: RHINOCEROS_ Tail5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &402524
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 195096}
  m_LocalRotation: {x: 1.1368684e-13, y: 1.1368684e-13, z: -1.2924697e-26, w: 1}
  m_LocalPosition: {x: -0.119578324, y: -0.0001020813, z: -2.828892e-10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 404522}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &196756
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 435838}
  m_Layer: 0
  m_Name: RHINOCEROS_ Head
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &435838
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 196756}
  m_LocalRotation: {x: -9.117626e-13, y: -0.00000094966475, z: 0.34239486, w: 0.9395562}
  m_LocalPosition: {x: -0.26976278, y: 0, z: 0}
  m_LocalScale: {x: 0.9999999, y: 0.9999999, z: 1}
  m_Children:
  - {fileID: 493298}
  m_Father: {fileID: 453786}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
