fileFormatVersion: 2
guid: 3f420de16bb280844a29cc4e4785c9f6
ModelImporter:
  serializedVersion: 20300
  internalIDToNameTable:
  - first:
      1: 100000
    second: //RootNode
  - first:
      1: 100002
    second: CROCODILE_
  - first:
      1: 100004
    second: CROCODILE_ Head
  - first:
      1: 100006
    second: CROCODILE_ L Calf
  - first:
      1: 100008
    second: CROCODILE_ L Clavicle
  - first:
      1: 100010
    second: CROCODILE_ L Finger0
  - first:
      1: 100012
    second: CROCODILE_ L Finger1
  - first:
      1: 100014
    second: CROCODILE_ L Foot
  - first:
      1: 100016
    second: CROCODILE_ L Forearm
  - first:
      1: 100018
    second: CROCODILE_ L Hand
  - first:
      1: 100020
    second: CROCODILE_ L Thigh
  - first:
      1: 100022
    second: CROCODILE_ L Toe0
  - first:
      1: 100024
    second: CROCODILE_ L Toe1
  - first:
      1: 100026
    second: CROCODILE_ L UpperArm
  - first:
      1: 100028
    second: CROCODILE_ Neck
  - first:
      1: 100030
    second: CROCODILE_ Neck1
  - first:
      1: 100032
    second: CROCOD<PERSON><PERSON>_ Pelvis
  - first:
      1: 100034
    second: CROCODILE_ Queue de cheval 1
  - first:
      1: 100036
    second: CROCODILE_ R Calf
  - first:
      1: 100038
    second: CROCODILE_ R Clavicle
  - first:
      1: 100040
    second: CROCODILE_ R Finger0
  - first:
      1: 100042
    second: CROCODILE_ R Finger1
  - first:
      1: 100044
    second: CROCODILE_ R Foot
  - first:
      1: 100046
    second: CROCODILE_ R Forearm
  - first:
      1: 100048
    second: CROCODILE_ R Hand
  - first:
      1: 100050
    second: CROCODILE_ R Thigh
  - first:
      1: 100052
    second: CROCODILE_ R Toe0
  - first:
      1: 100054
    second: CROCODILE_ R Toe1
  - first:
      1: 100056
    second: CROCODILE_ R UpperArm
  - first:
      1: 100058
    second: CROCODILE_ Spine
  - first:
      1: 100060
    second: CROCODILE_ Spine1
  - first:
      1: 100062
    second: CROCODILE_ Tail
  - first:
      1: 100064
    second: CROCODILE_ Tail1
  - first:
      1: 100066
    second: CROCODILE_ Tail2
  - first:
      1: 100068
    second: CROCODILE_ Tail3
  - first:
      1: 100070
    second: CROCODILE_ Tail4
  - first:
      1: 100072
    second: CROCODILE_ Tail5
  - first:
      1: 100074
    second: CROCODILE_ Tail6
  - first:
      1: 100076
    second: root
  - first:
      1: 100078
    second: SK_Crocodile_LOD0
  - first:
      1: 100080
    second: SK_Crocodile_LOD1
  - first:
      1: 100082
    second: SK_Crocodile_LOD2
  - first:
      4: 400000
    second: //RootNode
  - first:
      4: 400002
    second: CROCODILE_
  - first:
      4: 400004
    second: CROCODILE_ Head
  - first:
      4: 400006
    second: CROCODILE_ L Calf
  - first:
      4: 400008
    second: CROCODILE_ L Clavicle
  - first:
      4: 400010
    second: CROCODILE_ L Finger0
  - first:
      4: 400012
    second: CROCODILE_ L Finger1
  - first:
      4: 400014
    second: CROCODILE_ L Foot
  - first:
      4: 400016
    second: CROCODILE_ L Forearm
  - first:
      4: 400018
    second: CROCODILE_ L Hand
  - first:
      4: 400020
    second: CROCODILE_ L Thigh
  - first:
      4: 400022
    second: CROCODILE_ L Toe0
  - first:
      4: 400024
    second: CROCODILE_ L Toe1
  - first:
      4: 400026
    second: CROCODILE_ L UpperArm
  - first:
      4: 400028
    second: CROCODILE_ Neck
  - first:
      4: 400030
    second: CROCODILE_ Neck1
  - first:
      4: 400032
    second: CROCODILE_ Pelvis
  - first:
      4: 400034
    second: CROCODILE_ Queue de cheval 1
  - first:
      4: 400036
    second: CROCODILE_ R Calf
  - first:
      4: 400038
    second: CROCODILE_ R Clavicle
  - first:
      4: 400040
    second: CROCODILE_ R Finger0
  - first:
      4: 400042
    second: CROCODILE_ R Finger1
  - first:
      4: 400044
    second: CROCODILE_ R Foot
  - first:
      4: 400046
    second: CROCODILE_ R Forearm
  - first:
      4: 400048
    second: CROCODILE_ R Hand
  - first:
      4: 400050
    second: CROCODILE_ R Thigh
  - first:
      4: 400052
    second: CROCODILE_ R Toe0
  - first:
      4: 400054
    second: CROCODILE_ R Toe1
  - first:
      4: 400056
    second: CROCODILE_ R UpperArm
  - first:
      4: 400058
    second: CROCODILE_ Spine
  - first:
      4: 400060
    second: CROCODILE_ Spine1
  - first:
      4: 400062
    second: CROCODILE_ Tail
  - first:
      4: 400064
    second: CROCODILE_ Tail1
  - first:
      4: 400066
    second: CROCODILE_ Tail2
  - first:
      4: 400068
    second: CROCODILE_ Tail3
  - first:
      4: 400070
    second: CROCODILE_ Tail4
  - first:
      4: 400072
    second: CROCODILE_ Tail5
  - first:
      4: 400074
    second: CROCODILE_ Tail6
  - first:
      4: 400076
    second: root
  - first:
      4: 400078
    second: SK_Crocodile_LOD0
  - first:
      4: 400080
    second: SK_Crocodile_LOD1
  - first:
      4: 400082
    second: SK_Crocodile_LOD2
  - first:
      43: 4300000
    second: SK_Crocodile_LOD0
  - first:
      43: 4300002
    second: SK_Crocodile_LOD1
  - first:
      43: 4300004
    second: SK_Crocodile_LOD2
  - first:
      74: 7400000
    second: Take 001
  - first:
      95: 9500000
    second: //RootNode
  - first:
      137: 13700000
    second: SK_Crocodile_LOD0
  - first:
      137: 13700002
    second: SK_Crocodile_LOD1
  - first:
      137: 13700004
    second: SK_Crocodile_LOD2
  - first:
      205: 20500000
    second: //RootNode
  externalObjects: {}
  materials:
    materialImportMode: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 0
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages:
    - 0.25
    - 0.125
    - 0.01
    globalScale: 1
    meshCompression: 3
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 0
    importBlendShapes: 1
    importCameras: 0
    importLights: 0
    fileIdsGeneration: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    meshOptimizationFlags: -1
    indexFormat: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 0
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 4
    normalCalculationMode: 0
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 1
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips:
  - 8c4ec38a928f0df4991df55e15bb8631
  - b2240d2087937fa488965af21ddc2481
  - a7d3e0ac6b7ff7b4ea2579dbbeb6251d
  - ********************************
  - 32a8b745c316f044bb90fa9994b94e40
  - 05155d810d7c3a14eb69cab6aecfa973
  - b8bf6223a8ae61546b07dc22d1055bf6
  - 28997b4bd55d4064aa376bdb77702501
  - 436ea4519ceb9814cb4361836edc78a4
  - 3fffc150c1f7ba04db05bfd560d15ff5
  - 204997c4be068d340b17e907e89036ef
  - 659c6200de2205541a961cdd57a98eb7
  - 711cec52263ac924f85b8087e5b54b4f
  - b536f116968d28448b6279dfbf2a27fc
  - 79c2161b75aa77f489bdf33455756206
  - 24af5a4fca729a54eb8f8ff652893f82
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: root
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 0
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 2
  humanoidOversampling: 1
  avatarSetup: 1
  addHumanoidExtraRootOnlyWhenUsingAvatar: 0
  remapMaterialsIfMaterialImportModeIsNone: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
