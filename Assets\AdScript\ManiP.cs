using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class ManiP : MonoBehaviour
{
    public Button buttonSave;
    public InputField inputField;
    public GameObject M, F;

    void Update()
    {
        bool isInputFieldNotEmpty = !string.IsNullOrEmpty(inputField.text);
        bool isEitherObjectActive = (M != null && M.activeSelf) || (F != null && F.activeSelf);
        buttonSave.interactable = isInputFieldNotEmpty && isEitherObjectActive;
    }
}
