%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &103620
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 467860}
  m_Layer: 0
  m_Name: CROCODILE_ Tail3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &467860
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 103620}
  m_LocalRotation: {x: 2.4023868e-13, y: -0.000000004834201, z: 0.0017429335, w: 0.9999985}
  m_LocalPosition: {x: -0.50975156, y: -0.00035743712, z: -9.909854e-10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 475644}
  m_Father: {fileID: 464684}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &105654
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 486980}
  m_Layer: 0
  m_Name: root
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &486980
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 105654}
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 418278}
  m_Father: {fileID: 450312}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &111000
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 418278}
  m_Layer: 0
  m_Name: CROCODILE_
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &418278
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 111000}
  m_LocalRotation: {x: 0.50000036, y: -0.4999997, z: 0.4999997, w: 0.50000036}
  m_LocalPosition: {x: -0, y: 0.709, z: 0.599}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 434638}
  m_Father: {fileID: 486980}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &111764
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 464684}
  m_Layer: 0
  m_Name: CROCODILE_ Tail2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &464684
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 111764}
  m_LocalRotation: {x: -3.211719e-14, y: -0.000000015085531, z: 0.0054389713, w: 0.9999852}
  m_LocalPosition: {x: -0.46838638, y: -0.00040596008, z: -0.0000000011257362}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 467860}
  m_Father: {fileID: 403698}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &120684
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 455288}
  m_Layer: 0
  m_Name: CROCODILE_ Queue de cheval 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &455288
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 120684}
  m_LocalRotation: {x: -0.0000014960824, y: 0.000004550081, z: -0.800934, w: 0.5987526}
  m_LocalPosition: {x: 0.023976745, y: 0.10577575, z: -0.00000017451296}
  m_LocalScale: {x: 0.9999999, y: 0.9999999, z: 1}
  m_Children: []
  m_Father: {fileID: 416114}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &122498
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 411342}
  - component: {fileID: 13756976}
  m_Layer: 0
  m_Name: SK_Crocodile_LOD2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &411342
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 122498}
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 450312}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &13756976
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 122498}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 8f8782349933b2a4ab7fedba94d16f1f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 4
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 4300004, guid: 3f420de16bb280844a29cc4e4785c9f6, type: 3}
  m_Bones:
  - {fileID: 416114}
  - {fileID: 485998}
  - {fileID: 415766}
  - {fileID: 442502}
  - {fileID: 491402}
  - {fileID: 454558}
  - {fileID: 444320}
  - {fileID: 434682}
  - {fileID: 421536}
  - {fileID: 491536}
  - {fileID: 461802}
  - {fileID: 408302}
  - {fileID: 451920}
  - {fileID: 443182}
  - {fileID: 492524}
  - {fileID: 455288}
  - {fileID: 408764}
  - {fileID: 404456}
  - {fileID: 433422}
  - {fileID: 490082}
  - {fileID: 442232}
  - {fileID: 482744}
  - {fileID: 499410}
  - {fileID: 480258}
  - {fileID: 434638}
  - {fileID: 499872}
  - {fileID: 480212}
  - {fileID: 449430}
  - {fileID: 489528}
  - {fileID: 473694}
  - {fileID: 403698}
  - {fileID: 464684}
  - {fileID: 467860}
  - {fileID: 475644}
  - {fileID: 466394}
  - {fileID: 484586}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 434638}
  m_AABB:
    m_Center: {x: 0.17363024, y: 0.28062844, z: -0.042549074}
    m_Extent: {x: 3.218761, y: 1.1037246, z: 1.2966192}
  m_DirtyAABB: 0
--- !u!1 &125382
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 408764}
  m_Layer: 0
  m_Name: CROCODILE_ R Calf
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &408764
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 125382}
  m_LocalRotation: {x: 0.0000000010945705, y: -0.000000040107672, z: 0.3965819, w: 0.9179993}
  m_LocalPosition: {x: -0.34556174, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 489528}
  m_Father: {fileID: 480212}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &126754
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 434682}
  m_Layer: 0
  m_Name: CROCODILE_ L Hand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &434682
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 126754}
  m_LocalRotation: {x: -0.55814666, y: 0.17426744, z: 0.28313935, w: 0.7602205}
  m_LocalPosition: {x: -0.38797894, y: 0, z: -0.000000076293944}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1}
  m_Children:
  - {fileID: 442502}
  - {fileID: 491402}
  m_Father: {fileID: 444320}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &128390
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 499410}
  m_Layer: 0
  m_Name: CROCODILE_ R Toe1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &499410
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 128390}
  m_LocalRotation: {x: -0.23597135, y: -0.23597139, z: -0.6665715, w: 0.66657144}
  m_LocalPosition: {x: -0.10627258, y: 0.024658585, z: -0.112035364}
  m_LocalScale: {x: 0.99999994, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 489528}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &132144
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 495832}
  - component: {fileID: 13768390}
  m_Layer: 0
  m_Name: SK_Crocodile_LOD1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &495832
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 132144}
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 450312}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &13768390
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 132144}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 8f8782349933b2a4ab7fedba94d16f1f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 4
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 4300002, guid: 3f420de16bb280844a29cc4e4785c9f6, type: 3}
  m_Bones:
  - {fileID: 416114}
  - {fileID: 443182}
  - {fileID: 451920}
  - {fileID: 404456}
  - {fileID: 492524}
  - {fileID: 442232}
  - {fileID: 444320}
  - {fileID: 434682}
  - {fileID: 421536}
  - {fileID: 491536}
  - {fileID: 461802}
  - {fileID: 408302}
  - {fileID: 491402}
  - {fileID: 442502}
  - {fileID: 415766}
  - {fileID: 455288}
  - {fileID: 408764}
  - {fileID: 485998}
  - {fileID: 433422}
  - {fileID: 490082}
  - {fileID: 454558}
  - {fileID: 434638}
  - {fileID: 482744}
  - {fileID: 449430}
  - {fileID: 473694}
  - {fileID: 403698}
  - {fileID: 480212}
  - {fileID: 464684}
  - {fileID: 467860}
  - {fileID: 475644}
  - {fileID: 466394}
  - {fileID: 484586}
  - {fileID: 499872}
  - {fileID: 480258}
  - {fileID: 499410}
  - {fileID: 489528}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 434638}
  m_AABB:
    m_Center: {x: 0.17355895, y: -0.010968208, z: -0.0000015497208}
    m_Extent: {x: 3.2186897, y: 1.0897858, z: 1.2251018}
  m_DirtyAABB: 0
--- !u!1 &137950
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 475644}
  m_Layer: 0
  m_Name: CROCODILE_ Tail4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &475644
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 137950}
  m_LocalRotation: {x: 1.2724312e-14, y: 0.000000014251406, z: -0.005138233, w: 0.9999868}
  m_LocalPosition: {x: -0.4488835, y: -0.00032356262, z: -8.972711e-10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 466394}
  m_Father: {fileID: 467860}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &139260
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 489528}
  m_Layer: 0
  m_Name: CROCODILE_ R Foot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &489528
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 139260}
  m_LocalRotation: {x: 0.032067366, y: -0.12869556, z: 0.33547068, w: 0.9326675}
  m_LocalPosition: {x: -0.37484175, y: 0, z: -0.00000015258789}
  m_LocalScale: {x: 1, y: 1, z: 0.99999976}
  m_Children:
  - {fileID: 480258}
  - {fileID: 499410}
  m_Father: {fileID: 408764}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &140716
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 484586}
  m_Layer: 0
  m_Name: CROCODILE_ Tail6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &484586
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 140716}
  m_LocalRotation: {x: 1.691182e-13, y: -0.000000010938953, z: 0.003943955, w: 0.99999225}
  m_LocalPosition: {x: -0.46744445, y: -0.00032321928, z: -8.96689e-10}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_Children: []
  m_Father: {fileID: 466394}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &140754
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 490650}
  - component: {fileID: 13724136}
  m_Layer: 0
  m_Name: SK_Crocodile_LOD0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &490650
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 140754}
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 450312}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &13724136
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 140754}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 8f8782349933b2a4ab7fedba94d16f1f, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 4
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 4300000, guid: 3f420de16bb280844a29cc4e4785c9f6, type: 3}
  m_Bones:
  - {fileID: 416114}
  - {fileID: 443182}
  - {fileID: 451920}
  - {fileID: 404456}
  - {fileID: 492524}
  - {fileID: 442232}
  - {fileID: 444320}
  - {fileID: 434682}
  - {fileID: 421536}
  - {fileID: 491536}
  - {fileID: 461802}
  - {fileID: 408302}
  - {fileID: 491402}
  - {fileID: 442502}
  - {fileID: 415766}
  - {fileID: 455288}
  - {fileID: 408764}
  - {fileID: 485998}
  - {fileID: 433422}
  - {fileID: 490082}
  - {fileID: 454558}
  - {fileID: 434638}
  - {fileID: 482744}
  - {fileID: 449430}
  - {fileID: 473694}
  - {fileID: 403698}
  - {fileID: 480212}
  - {fileID: 464684}
  - {fileID: 467860}
  - {fileID: 475644}
  - {fileID: 466394}
  - {fileID: 484586}
  - {fileID: 499872}
  - {fileID: 480258}
  - {fileID: 499410}
  - {fileID: 489528}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 434638}
  m_AABB:
    m_Center: {x: 0.17355895, y: -0.010968208, z: -0.0000015497208}
    m_Extent: {x: 3.2186897, y: 1.0897858, z: 1.2251018}
  m_DirtyAABB: 0
--- !u!1 &141968
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 416114}
  m_Layer: 0
  m_Name: CROCODILE_ Head
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &416114
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 141968}
  m_LocalRotation: {x: 0.0000000037679047, y: -0.0000020947957, z: 0.4941888, w: 0.86935467}
  m_LocalPosition: {x: -0.26104409, y: -0.05511772, z: -0.000000080598475}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_Children:
  - {fileID: 455288}
  m_Father: {fileID: 443182}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &146564
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 408302}
  m_Layer: 0
  m_Name: CROCODILE_ L UpperArm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &408302
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 146564}
  m_LocalRotation: {x: -0.14700225, y: -0.14762917, z: 0.058852945, w: 0.9762849}
  m_LocalPosition: {x: -0.19886409, y: 0, z: 0}
  m_LocalScale: {x: 1.0000001, y: 0.99999994, z: 1.0000001}
  m_Children:
  - {fileID: 444320}
  m_Father: {fileID: 415766}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &147620
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 491536}
  m_Layer: 0
  m_Name: CROCODILE_ L Toe0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &491536
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 147620}
  m_LocalRotation: {x: -0.000000018079106, y: 0.000000018799389, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -0.10627256, y: 0.16671966, z: -0.008000869}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 454558}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &150374
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 403698}
  m_Layer: 0
  m_Name: CROCODILE_ Tail1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &403698
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 150374}
  m_LocalRotation: {x: 2.6069742e-13, y: 0.000000038003503, z: -0.013701869, w: 0.9999061}
  m_LocalPosition: {x: -0.4328202, y: -0.0003728485, z: -0.0000000010340591}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 464684}
  m_Father: {fileID: 473694}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &150832
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 434638}
  m_Layer: 0
  m_Name: CROCODILE_ Pelvis
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &434638
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 150832}
  m_LocalRotation: {x: -0.5, y: 0.5, z: 0.4999993, w: 0.5000007}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 421536}
  - {fileID: 480212}
  - {fileID: 442232}
  - {fileID: 473694}
  m_Father: {fileID: 418278}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &151498
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 473694}
  m_Layer: 0
  m_Name: CROCODILE_ Tail
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &473694
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 151498}
  m_LocalRotation: {x: 0.00000070799524, y: -0.00000069825376, z: 0.9999753, w: -0.007024458}
  m_LocalPosition: {x: 0.24495597, y: 0.01036171, z: 0.00000032975797}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 403698}
  m_Father: {fileID: 434638}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &153552
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 482744}
  m_Layer: 0
  m_Name: CROCODILE_ R UpperArm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &482744
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 153552}
  m_LocalRotation: {x: 0.14700224, y: 0.1476291, z: 0.05885294, w: 0.976285}
  m_LocalPosition: {x: -0.19886409, y: 0, z: 0}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1.0000001}
  m_Children:
  - {fileID: 449430}
  m_Father: {fileID: 404456}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &156916
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 433422}
  m_Layer: 0
  m_Name: CROCODILE_ R Finger0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &433422
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 156916}
  m_LocalRotation: {x: -0.47311246, y: 0.54398066, z: -0.07851141, w: 0.68853885}
  m_LocalPosition: {x: -0.027628899, y: 0.036188506, z: 0.11140762}
  m_LocalScale: {x: 1, y: 0.99999994, z: 1.0000001}
  m_Children: []
  m_Father: {fileID: 499872}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &163008
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 421536}
  m_Layer: 0
  m_Name: CROCODILE_ L Thigh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &421536
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 163008}
  m_LocalRotation: {x: 0.73012626, y: -0.13649844, z: -0.66120315, w: -0.10532933}
  m_LocalPosition: {x: 0.00000045776366, y: 0.0000004196167, z: 0.304369}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1.0000001}
  m_Children:
  - {fileID: 485998}
  m_Father: {fileID: 434638}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &163886
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 480258}
  m_Layer: 0
  m_Name: CROCODILE_ R Toe0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &480258
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 163886}
  m_LocalRotation: {x: 0.00000000939455, y: -0.00000002221559, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -0.10627256, y: 0.16671973, z: 0.008000869}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 489528}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &165800
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 492524}
  m_Layer: 0
  m_Name: CROCODILE_ Spine1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &492524
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 165800}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.5551264, y: -0.0004420471, z: -0.0000000012260716}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 415766}
  - {fileID: 451920}
  - {fileID: 404456}
  m_Father: {fileID: 442232}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &173412
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 499872}
  m_Layer: 0
  m_Name: CROCODILE_ R Hand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &499872
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 173412}
  m_LocalRotation: {x: 0.55814666, y: -0.17426749, z: 0.28313932, w: 0.7602206}
  m_LocalPosition: {x: -0.38797894, y: 0.000000038146972, z: 0.000000076293944}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_Children:
  - {fileID: 433422}
  - {fileID: 490082}
  m_Father: {fileID: 449430}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &173890
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 454558}
  m_Layer: 0
  m_Name: CROCODILE_ L Foot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &454558
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 173890}
  m_LocalRotation: {x: -0.032067277, y: 0.12869568, z: 0.33547047, w: 0.93266755}
  m_LocalPosition: {x: -0.37484187, y: -0.000000038146972, z: -0.000000076293944}
  m_LocalScale: {x: 1.0000001, y: 0.99999994, z: 0.99999994}
  m_Children:
  - {fileID: 491536}
  - {fileID: 461802}
  m_Father: {fileID: 485998}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &177524
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 451920}
  m_Layer: 0
  m_Name: CROCODILE_ Neck
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &451920
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 177524}
  m_LocalRotation: {x: 3.632635e-12, y: -0.00000012819756, z: 0.030264545, w: 0.99954194}
  m_LocalPosition: {x: -0.55502385, y: -0.113351285, z: -0.0000001660249}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 443182}
  m_Father: {fileID: 492524}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &179064
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 443182}
  m_Layer: 0
  m_Name: CROCODILE_ Neck1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &443182
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 179064}
  m_LocalRotation: {x: -0.000000003966328, y: -0.000000014079295, z: 0.0033186777, w: 0.9999945}
  m_LocalPosition: {x: -0.26108643, y: -0.0002078247, z: -5.7487337e-10}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_Children:
  - {fileID: 416114}
  m_Father: {fileID: 451920}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &183572
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 480212}
  m_Layer: 0
  m_Name: CROCODILE_ R Thigh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &480212
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 183572}
  m_LocalRotation: {x: 0.7301271, y: -0.13649747, z: 0.6612023, w: 0.10533045}
  m_LocalPosition: {x: -0.00000045776366, y: -0.0000004959106, z: -0.304369}
  m_LocalScale: {x: 1, y: 0.99999994, z: 1.0000001}
  m_Children:
  - {fileID: 408764}
  m_Father: {fileID: 434638}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &185296
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 442232}
  m_Layer: 0
  m_Name: CROCODILE_ Spine
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &442232
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 185296}
  m_LocalRotation: {x: -0.0000020804762, y: 0.000000693676, z: -0.00039815903, w: 0.99999994}
  m_LocalPosition: {x: -0.34088534, y: 0.024486313, z: 0.00000040609368}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 492524}
  m_Father: {fileID: 434638}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &186316
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 491402}
  m_Layer: 0
  m_Name: CROCODILE_ L Finger1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &491402
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 186316}
  m_LocalRotation: {x: -0.00039814418, y: 0.000000029796388, z: 0.000000014913025, w: 0.99999994}
  m_LocalPosition: {x: -0.13394123, y: 0, z: -0.000000038146972}
  m_LocalScale: {x: 1, y: 0.99999994, z: 0.99999994}
  m_Children: []
  m_Father: {fileID: 434682}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &186744
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 415766}
  m_Layer: 0
  m_Name: CROCODILE_ L Clavicle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &415766
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 186744}
  m_LocalRotation: {x: -0.6427875, y: -0.0002569967, z: 0.7660444, w: -0.00030411335}
  m_LocalPosition: {x: -0.5551266, y: -0.00000022888183, z: 0.09126599}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 408302}
  m_Father: {fileID: 492524}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &187500
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 466394}
  m_Layer: 0
  m_Name: CROCODILE_ Tail5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &466394
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 187500}
  m_LocalRotation: {x: -7.7120805e-14, y: -0.000000022484715, z: 0.008106688, w: 0.99996716}
  m_LocalPosition: {x: -0.4063086, y: -0.0003721237, z: -0.0000000010320218}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 484586}
  m_Father: {fileID: 475644}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &188772
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 450312}
  - component: {fileID: 9582284}
  - component: {fileID: 20557902}
  m_Layer: 0
  m_Name: Crocodile_LEGACY
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &450312
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 188772}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 4.74, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 486980}
  - {fileID: 490650}
  - {fileID: 495832}
  - {fileID: 411342}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!95 &9582284
Animator:
  serializedVersion: 3
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 188772}
  m_Enabled: 1
  m_Avatar: {fileID: 9000000, guid: 3f420de16bb280844a29cc4e4785c9f6, type: 3}
  m_Controller: {fileID: 0}
  m_CullingMode: 1
  m_UpdateMode: 0
  m_ApplyRootMotion: 1
  m_LinearVelocityBlending: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorControllerStateOnDisable: 0
--- !u!205 &20557902
LODGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 188772}
  serializedVersion: 2
  m_LocalReferencePoint: {x: 0.0425483, y: 0.4572006, z: -0.8826308}
  m_Size: 6.4375286
  m_FadeMode: 0
  m_AnimateCrossFading: 0
  m_LastLODIsBillboard: 0
  m_LODs:
  - screenRelativeHeight: 0.7350008
    fadeTransitionWidth: 0
    renderers:
    - renderer: {fileID: 13724136}
  - screenRelativeHeight: 0.49071854
    fadeTransitionWidth: 0
    renderers:
    - renderer: {fileID: 13768390}
  - screenRelativeHeight: 0.03617478
    fadeTransitionWidth: 0
    renderers:
    - renderer: {fileID: 13756976}
  m_Enabled: 1
--- !u!1 &189610
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 485998}
  m_Layer: 0
  m_Name: CROCODILE_ L Calf
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &485998
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 189610}
  m_LocalRotation: {x: -0.000000012749163, y: 0.000000010724487, z: 0.3965822, w: 0.9179992}
  m_LocalPosition: {x: -0.34556177, y: 0.000000038146972, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 454558}
  m_Father: {fileID: 421536}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &189948
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 449430}
  m_Layer: 0
  m_Name: CROCODILE_ R Forearm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &449430
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 189948}
  m_LocalRotation: {x: 0.000000009780585, y: -0.0000000039195007, z: 0.39333066, w: 0.91939706}
  m_LocalPosition: {x: -0.29908144, y: -0.000000038146972, z: 0}
  m_LocalScale: {x: 0.9999999, y: 0.9999999, z: 0.99999994}
  m_Children:
  - {fileID: 499872}
  m_Father: {fileID: 482744}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &192074
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 404456}
  m_Layer: 0
  m_Name: CROCODILE_ R Clavicle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &404456
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 192074}
  m_LocalRotation: {x: 0.6427875, y: 0.000254872, z: 0.7660444, w: -0.00030589622}
  m_LocalPosition: {x: -0.5551266, y: 0.0000002670288, z: -0.09126599}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 482744}
  m_Father: {fileID: 492524}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &194694
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 461802}
  m_Layer: 0
  m_Name: CROCODILE_ L Toe1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &461802
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 194694}
  m_LocalRotation: {x: 0.23597133, y: 0.23597138, z: -0.6665715, w: 0.66657144}
  m_LocalPosition: {x: -0.10627256, y: 0.024658585, z: 0.11203552}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 454558}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &195386
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 490082}
  m_Layer: 0
  m_Name: CROCODILE_ R Finger1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &490082
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 195386}
  m_LocalRotation: {x: 0.00039814418, y: 0, z: -0, w: 0.99999994}
  m_LocalPosition: {x: -0.13394131, y: 0, z: 0.000000038146972}
  m_LocalScale: {x: 0.99999994, y: 0.99999994, z: 0.99999994}
  m_Children: []
  m_Father: {fileID: 499872}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &197344
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 442502}
  m_Layer: 0
  m_Name: CROCODILE_ L Finger0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &442502
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 197344}
  m_LocalRotation: {x: 0.4731124, y: -0.5439806, z: -0.078511424, w: 0.68853885}
  m_LocalPosition: {x: -0.027628899, y: 0.036188506, z: -0.111407734}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1}
  m_Children: []
  m_Father: {fileID: 434682}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &199476
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 444320}
  m_Layer: 0
  m_Name: CROCODILE_ L Forearm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &444320
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 199476}
  m_LocalRotation: {x: -0.0000000058610836, y: 0.000000013700083, z: 0.3933307, w: 0.91939706}
  m_LocalPosition: {x: -0.2990815, y: -0.000000076293944, z: 0.000000076293944}
  m_LocalScale: {x: 1, y: 1, z: 1.0000001}
  m_Children:
  - {fileID: 434682}
  m_Father: {fileID: 408302}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
