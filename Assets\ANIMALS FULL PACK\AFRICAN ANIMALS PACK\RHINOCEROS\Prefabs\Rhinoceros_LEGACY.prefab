%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &103064
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 492046}
  m_Layer: 0
  m_Name: RHINOCEROS_ Spine1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &492046
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 103064}
  m_LocalRotation: {x: 2.163033e-14, y: -0.00000012098275, z: 0.043619394, w: 0.99904823}
  m_LocalPosition: {x: -0.8694532, y: -0.00068954466, z: -0.0000000019128492}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 483998}
  - {fileID: 401954}
  - {fileID: 437312}
  m_Father: {fileID: 478332}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &106622
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 426270}
  m_Layer: 0
  m_Name: RHINOCEROS_ L Calf
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &426270
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 106622}
  m_LocalRotation: {x: -5.8857913e-10, y: 0.000000007657298, z: 0.3161731, w: 0.94870156}
  m_LocalPosition: {x: -0.64570385, y: -0.000000076293944, z: 0}
  m_LocalScale: {x: 0.99999976, y: 0.9999999, z: 0.99999994}
  m_Children:
  - {fileID: 469412}
  m_Father: {fileID: 439392}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &108864
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 478332}
  m_Layer: 0
  m_Name: RHINOCEROS_ Spine
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &478332
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 108864}
  m_LocalRotation: {x: -0.0000020804762, y: 0.000000693676, z: -0.00039815903, w: 0.99999994}
  m_LocalPosition: {x: -0.39750692, y: -0.0006924438, z: 0.0000005522228}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 492046}
  m_Father: {fileID: 455928}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &111960
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 498858}
  m_Layer: 0
  m_Name: RHINOCEROS_ L UpperArm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &498858
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 111960}
  m_LocalRotation: {x: -0.5713251, y: -0.5030293, z: 0.11491363, w: 0.6382351}
  m_LocalPosition: {x: -0.378026, y: -0.000000076293944, z: 0}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1.0000001}
  m_Children:
  - {fileID: 499286}
  m_Father: {fileID: 483998}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &112252
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 401734}
  m_Layer: 0
  m_Name: RHINOCEROS_ Tail1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &401734
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 112252}
  m_LocalRotation: {x: -2.4998054e-13, y: -0.00000023644559, z: 0.08524861, w: 0.99635977}
  m_LocalPosition: {x: -0.10077545, y: -0.00009735107, z: -2.7008354e-10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 432898}
  m_Father: {fileID: 449138}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &114688
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 455928}
  m_Layer: 0
  m_Name: RHINOCEROS_ Pelvis
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &455928
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 114688}
  m_LocalRotation: {x: -0.5, y: 0.5, z: 0.4999993, w: 0.5000007}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 439392}
  - {fileID: 481628}
  - {fileID: 478332}
  - {fileID: 449138}
  m_Father: {fileID: 402842}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &115414
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 410462}
  m_Layer: 0
  m_Name: RHINOCEROS_ Head
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &410462
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 115414}
  m_LocalRotation: {x: -9.117626e-13, y: -0.00000094966475, z: 0.34239486, w: 0.9395562}
  m_LocalPosition: {x: -0.26976278, y: 0, z: 0}
  m_LocalScale: {x: 0.9999999, y: 0.9999999, z: 1}
  m_Children:
  - {fileID: 451040}
  m_Father: {fileID: 496282}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &115514
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 464352}
  m_Layer: 0
  m_Name: RHINOCEROS_ L Finger0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &464352
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 115514}
  m_LocalRotation: {x: 0.0005332227, y: 0.017039273, z: -0.054471064, w: 0.9983698}
  m_LocalPosition: {x: -0.19634135, y: -0.014610977, z: -0.0008666992}
  m_LocalScale: {x: 0.99999994, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 409852}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &117202
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 451040}
  m_Layer: 0
  m_Name: RHINOCEROS_ Queue de cheval 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &451040
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 117202}
  m_LocalRotation: {x: 1.2566133e-12, y: 0.0000017762535, z: -0.64041495, w: 0.7680291}
  m_LocalPosition: {x: 0.16581771, y: 0.29339346, z: 0.00000035852207}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 410462}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &121386
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 439392}
  m_Layer: 0
  m_Name: RHINOCEROS_ L Thigh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &439392
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 121386}
  m_LocalRotation: {x: 0.7358285, y: -0.67345047, z: -0.015456787, w: 0.06915157}
  m_LocalPosition: {x: 0.0000003814697, y: 0.00000045776366, z: 0.3210737}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1}
  m_Children:
  - {fileID: 426270}
  m_Father: {fileID: 455928}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &124854
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 490712}
  m_Layer: 0
  m_Name: RHINOCEROS_ R HorseLink
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &490712
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 124854}
  m_LocalRotation: {x: 0.0000000028093374, y: -0.000000005260177, z: -0.39449623, w: 0.91889757}
  m_LocalPosition: {x: -0.478479, y: 0, z: -0.000000019073486}
  m_LocalScale: {x: 0.9999998, y: 0.9999999, z: 0.99999994}
  m_Children:
  - {fileID: 426632}
  m_Father: {fileID: 412900}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &127904
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 494346}
  - component: {fileID: 13758154}
  m_Layer: 0
  m_Name: Sk_Rhinoceros_LOD1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &494346
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 127904}
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 415724}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &13758154
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 127904}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 8b5a5826a4d5a7642802c207d310a4fe, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 4
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 4300004, guid: 13a52ff2ed9b95a4e9b0243f68f23ef3, type: 3}
  m_Bones:
  - {fileID: 410462}
  - {fileID: 426270}
  - {fileID: 483998}
  - {fileID: 464352}
  - {fileID: 402068}
  - {fileID: 499286}
  - {fileID: 409852}
  - {fileID: 469412}
  - {fileID: 439392}
  - {fileID: 498858}
  - {fileID: 401954}
  - {fileID: 496282}
  - {fileID: 455928}
  - {fileID: 451040}
  - {fileID: 412900}
  - {fileID: 437312}
  - {fileID: 449216}
  - {fileID: 426632}
  - {fileID: 414230}
  - {fileID: 420702}
  - {fileID: 478332}
  - {fileID: 492046}
  - {fileID: 481628}
  - {fileID: 449138}
  - {fileID: 401734}
  - {fileID: 432898}
  - {fileID: 490712}
  - {fileID: 493366}
  - {fileID: 481246}
  - {fileID: 452666}
  - {fileID: 465016}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 455928}
  m_AABB:
    m_Center: {x: -1.6484936, y: 0.30684394, z: -0.14650679}
    m_Extent: {x: 2.6371167, y: 1.4763579, z: 1.2954311}
  m_DirtyAABB: 0
--- !u!1 &129346
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 412900}
  m_Layer: 0
  m_Name: RHINOCEROS_ R Calf
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &412900
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 129346}
  m_LocalRotation: {x: 0.0000000017671181, y: 5.8884686e-10, z: 0.31613475, w: 0.9487143}
  m_LocalPosition: {x: -0.6457041, y: 0.000000076293944, z: 0.000000019073486}
  m_LocalScale: {x: 0.99999994, y: 0.99999994, z: 0.99999994}
  m_Children:
  - {fileID: 490712}
  m_Father: {fileID: 481628}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &130176
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 481628}
  m_Layer: 0
  m_Name: RHINOCEROS_ R Thigh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &481628
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 130176}
  m_LocalRotation: {x: 0.7358165, y: -0.67346376, z: 0.015453811, w: -0.06915085}
  m_LocalPosition: {x: -0.0000005340576, y: -0.00000045776366, z: -0.3210737}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 412900}
  m_Father: {fileID: 455928}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &131554
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 465016}
  m_Layer: 0
  m_Name: RHINOCEROS_ Tail5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &465016
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 131554}
  m_LocalRotation: {x: 1.1368684e-13, y: 1.1368684e-13, z: -1.2924697e-26, w: 1}
  m_LocalPosition: {x: -0.119578324, y: -0.0001020813, z: -2.828892e-10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 452666}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &133946
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 449138}
  m_Layer: 0
  m_Name: RHINOCEROS_ Tail
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &449138
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 133946}
  m_LocalRotation: {x: 0.0000012537814, y: -0.00000086129387, z: 0.959115, w: -0.28301653}
  m_LocalPosition: {x: 0.43539283, y: -0.009214325, z: -0.0000009792038}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 401734}
  m_Father: {fileID: 455928}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &137800
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 426632}
  m_Layer: 0
  m_Name: RHINOCEROS_ R Foot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &426632
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 137800}
  m_LocalRotation: {x: -0.06279548, y: 0.032825593, z: 0.12741184, w: 0.9893156}
  m_LocalPosition: {x: -0.27068278, y: 0, z: -0.000000057220458}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 490712}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &140884
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 499286}
  m_Layer: 0
  m_Name: RHINOCEROS_ L Forearm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &499286
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 140884}
  m_LocalRotation: {x: -0.000000006076822, y: 0.000000021690525, z: 0.14825377, w: 0.98894936}
  m_LocalPosition: {x: -0.7169841, y: 0.000000076293944, z: 0.000000019073486}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1}
  m_Children:
  - {fileID: 409852}
  m_Father: {fileID: 498858}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &141688
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 474800}
  - component: {fileID: 13711838}
  m_Layer: 0
  m_Name: Sk_Rhinoceros_LOD2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &474800
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 141688}
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 415724}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &13711838
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 141688}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 8b5a5826a4d5a7642802c207d310a4fe, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 4
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 4300002, guid: 13a52ff2ed9b95a4e9b0243f68f23ef3, type: 3}
  m_Bones:
  - {fileID: 410462}
  - {fileID: 426270}
  - {fileID: 483998}
  - {fileID: 464352}
  - {fileID: 402068}
  - {fileID: 499286}
  - {fileID: 409852}
  - {fileID: 469412}
  - {fileID: 439392}
  - {fileID: 498858}
  - {fileID: 401954}
  - {fileID: 496282}
  - {fileID: 455928}
  - {fileID: 451040}
  - {fileID: 412900}
  - {fileID: 437312}
  - {fileID: 449216}
  - {fileID: 426632}
  - {fileID: 414230}
  - {fileID: 420702}
  - {fileID: 478332}
  - {fileID: 492046}
  - {fileID: 481628}
  - {fileID: 449138}
  - {fileID: 401734}
  - {fileID: 432898}
  - {fileID: 490712}
  - {fileID: 493366}
  - {fileID: 481246}
  - {fileID: 452666}
  - {fileID: 465016}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 455928}
  m_AABB:
    m_Center: {x: -1.6484936, y: 0.2795416, z: -0.0013185143}
    m_Extent: {x: 2.6371167, y: 1.3708946, z: 1.1006308}
  m_DirtyAABB: 0
--- !u!1 &156456
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 414230}
  m_Layer: 0
  m_Name: RHINOCEROS_ R Forearm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &414230
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 156456}
  m_LocalRotation: {x: 0.000000002854512, y: -0.000000022173605, z: 0.14831166, w: 0.98894066}
  m_LocalPosition: {x: -0.71698403, y: 0.00000015258789, z: 0.000000019073486}
  m_LocalScale: {x: 1.0000002, y: 0.99999994, z: 1}
  m_Children:
  - {fileID: 493366}
  m_Father: {fileID: 420702}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &156986
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 449216}
  m_Layer: 0
  m_Name: RHINOCEROS_ R Finger0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &449216
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 156986}
  m_LocalRotation: {x: -0.00053321937, y: -0.01703927, z: -0.054471064, w: 0.9983698}
  m_LocalPosition: {x: -0.19634135, y: -0.014610977, z: 0.0008666801}
  m_LocalScale: {x: 0.9999999, y: 0.99999994, z: 0.9999999}
  m_Children: []
  m_Father: {fileID: 493366}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &158558
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 469412}
  m_Layer: 0
  m_Name: RHINOCEROS_ L HorseLink
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &469412
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 158558}
  m_LocalRotation: {x: -0.000000011869238, y: -0.0000000111205445, z: -0.39451736, w: 0.9188885}
  m_LocalPosition: {x: -0.4784789, y: 0.000000076293944, z: 0.000000019073486}
  m_LocalScale: {x: 1, y: 0.9999999, z: 0.99999994}
  m_Children:
  - {fileID: 402068}
  m_Father: {fileID: 426270}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &160478
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 493366}
  m_Layer: 0
  m_Name: RHINOCEROS_ R Hand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &493366
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 160478}
  m_LocalRotation: {x: 0.025051994, y: -0.07243901, z: 0.0700217, w: 0.99459636}
  m_LocalPosition: {x: -0.40074193, y: 0, z: 0}
  m_LocalScale: {x: 0.99999994, y: 1, z: 1}
  m_Children:
  - {fileID: 449216}
  m_Father: {fileID: 414230}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &166332
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 432898}
  m_Layer: 0
  m_Name: RHINOCEROS_ Tail2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &432898
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 166332}
  m_LocalRotation: {x: 3.5566905e-13, y: -0.00000014528268, z: 0.05238052, w: 0.9986272}
  m_LocalPosition: {x: -0.124336466, y: -0.0001095581, z: -3.0442607e-10}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_Children:
  - {fileID: 481246}
  m_Father: {fileID: 401734}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &171100
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 452666}
  m_Layer: 0
  m_Name: RHINOCEROS_ Tail4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &452666
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 171100}
  m_LocalRotation: {x: 4.6268514e-14, y: 0.000000096592814, z: -0.034825843, w: 0.9993934}
  m_LocalPosition: {x: -0.123738706, y: -0.000094909665, z: -2.6309863e-10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 465016}
  m_Father: {fileID: 481246}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &171502
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 483998}
  m_Layer: 0
  m_Name: RHINOCEROS_ L Clavicle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &483998
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 171502}
  m_LocalRotation: {x: 0.70820487, y: -0.25158745, z: -0.58485264, w: 0.30511805}
  m_LocalPosition: {x: -0.6963283, y: -0.15458938, z: 0.14766951}
  m_LocalScale: {x: 0.99999994, y: 0.99999994, z: 1}
  m_Children:
  - {fileID: 498858}
  m_Father: {fileID: 492046}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &180568
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 415724}
  - component: {fileID: 9539420}
  - component: {fileID: 20521724}
  m_Layer: 0
  m_Name: Rhinoceros_LEGACY
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &415724
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 180568}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 424114}
  - {fileID: 401842}
  - {fileID: 494346}
  - {fileID: 474800}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!95 &9539420
Animator:
  serializedVersion: 3
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 180568}
  m_Enabled: 1
  m_Avatar: {fileID: 9000000, guid: 13a52ff2ed9b95a4e9b0243f68f23ef3, type: 3}
  m_Controller: {fileID: 0}
  m_CullingMode: 1
  m_UpdateMode: 0
  m_ApplyRootMotion: 1
  m_LinearVelocityBlending: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorControllerStateOnDisable: 0
--- !u!205 &20521724
LODGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 180568}
  serializedVersion: 2
  m_LocalReferencePoint: {x: 0.15274596, y: 1.1901535, z: 0.6003516}
  m_Size: 5.2763343
  m_FadeMode: 0
  m_AnimateCrossFading: 0
  m_LastLODIsBillboard: 0
  m_LODs:
  - screenRelativeHeight: 0.7894107
    fadeTransitionWidth: 0
    renderers:
    - renderer: {fileID: 13723004}
  - screenRelativeHeight: 0.37513182
    fadeTransitionWidth: 0
    renderers:
    - renderer: {fileID: 13758154}
  - screenRelativeHeight: 0.047997158
    fadeTransitionWidth: 0
    renderers:
    - renderer: {fileID: 13711838}
  m_Enabled: 1
--- !u!1 &181482
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 401842}
  - component: {fileID: 13723004}
  m_Layer: 0
  m_Name: Sk_Rhinoceros_LOD0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &401842
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 181482}
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 415724}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &13723004
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 181482}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 8b5a5826a4d5a7642802c207d310a4fe, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 4
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 4300000, guid: 13a52ff2ed9b95a4e9b0243f68f23ef3, type: 3}
  m_Bones:
  - {fileID: 410462}
  - {fileID: 426270}
  - {fileID: 483998}
  - {fileID: 464352}
  - {fileID: 402068}
  - {fileID: 499286}
  - {fileID: 409852}
  - {fileID: 469412}
  - {fileID: 439392}
  - {fileID: 498858}
  - {fileID: 401954}
  - {fileID: 496282}
  - {fileID: 455928}
  - {fileID: 451040}
  - {fileID: 412900}
  - {fileID: 437312}
  - {fileID: 449216}
  - {fileID: 426632}
  - {fileID: 414230}
  - {fileID: 420702}
  - {fileID: 478332}
  - {fileID: 492046}
  - {fileID: 481628}
  - {fileID: 449138}
  - {fileID: 401734}
  - {fileID: 432898}
  - {fileID: 493366}
  - {fileID: 490712}
  - {fileID: 481246}
  - {fileID: 452666}
  - {fileID: 465016}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 455928}
  m_AABB:
    m_Center: {x: -1.6495403, y: 0.30684388, z: -0.15274656}
    m_Extent: {x: 2.6381633, y: 1.476358, z: 1.307219}
  m_DirtyAABB: 0
--- !u!1 &182642
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 496282}
  m_Layer: 0
  m_Name: RHINOCEROS_ Neck1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &496282
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 182642}
  m_LocalRotation: {x: 1.904718e-13, y: 0.00000012098272, z: -0.043619383, w: 0.99904823}
  m_LocalPosition: {x: -0.26978117, y: -0.00021392822, z: -5.934271e-10}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_Children:
  - {fileID: 410462}
  m_Father: {fileID: 401954}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &183370
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 437312}
  m_Layer: 0
  m_Name: RHINOCEROS_ R Clavicle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &437312
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 183370}
  m_LocalRotation: {x: 0.70820403, y: -0.2515891, z: 0.5848519, w: -0.30512002}
  m_LocalPosition: {x: -0.6963283, y: -0.15458846, z: -0.14767034}
  m_LocalScale: {x: 1, y: 0.99999994, z: 1}
  m_Children:
  - {fileID: 420702}
  m_Father: {fileID: 492046}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &184190
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 401954}
  m_Layer: 0
  m_Name: RHINOCEROS_ Neck
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &401954
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 184190}
  m_LocalRotation: {x: 2.163033e-14, y: 0.00000012098275, z: -0.043619394, w: 0.99904823}
  m_LocalPosition: {x: -0.8526626, y: 0.19439414, z: 0.00000028302455}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 496282}
  m_Father: {fileID: 492046}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &186804
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 409852}
  m_Layer: 0
  m_Name: RHINOCEROS_ L Hand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &409852
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 186804}
  m_LocalRotation: {x: -0.025053948, y: 0.072440535, z: 0.07004997, w: 0.9945943}
  m_LocalPosition: {x: -0.400742, y: -0.000000076293944, z: 0.000000019073486}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1}
  m_Children:
  - {fileID: 464352}
  m_Father: {fileID: 499286}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &188524
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 420702}
  m_Layer: 0
  m_Name: RHINOCEROS_ R UpperArm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &420702
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 188524}
  m_LocalRotation: {x: 0.5713096, y: 0.50304705, z: 0.114894204, w: 0.63823843}
  m_LocalPosition: {x: -0.37802592, y: 0.000000076293944, z: -0.000000076293944}
  m_LocalScale: {x: 1.0000001, y: 1.0000002, z: 1}
  m_Children:
  - {fileID: 414230}
  m_Father: {fileID: 437312}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &189990
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 424114}
  m_Layer: 0
  m_Name: root
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &424114
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 189990}
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 402842}
  m_Father: {fileID: 415724}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &190160
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 402068}
  m_Layer: 0
  m_Name: RHINOCEROS_ L Foot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &402068
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 190160}
  m_LocalRotation: {x: 0.06279624, y: -0.03282303, z: 0.12741245, w: 0.9893156}
  m_LocalPosition: {x: -0.2706828, y: 0.000000076293944, z: 0.000000019073486}
  m_LocalScale: {x: 0.9999999, y: 1, z: 0.99999994}
  m_Children: []
  m_Father: {fileID: 469412}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &191966
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 402842}
  m_Layer: 0
  m_Name: RHINOCEROS_
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &402842
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 191966}
  m_LocalRotation: {x: 0.50000036, y: -0.4999997, z: 0.4999997, w: 0.50000036}
  m_LocalPosition: {x: -0, y: 1.0491885, z: 1.497}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 455928}
  m_Father: {fileID: 424114}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &196280
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 481246}
  m_Layer: 0
  m_Name: RHINOCEROS_ Tail3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &481246
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 196280}
  m_LocalRotation: {x: -2.2713426e-13, y: -0.000000024450902, z: 0.008815647, w: 0.99996114}
  m_LocalPosition: {x: -0.13848723, y: -0.00009857178, z: -2.7299393e-10}
  m_LocalScale: {x: 0.9999999, y: 0.9999999, z: 1}
  m_Children:
  - {fileID: 452666}
  m_Father: {fileID: 432898}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
