fileFormatVersion: 2
guid: 13a52ff2ed9b95a4e9b0243f68f23ef3
ModelImporter:
  serializedVersion: 20300
  internalIDToNameTable:
  - first:
      1: 100000
    second: //RootNode
  - first:
      1: 100002
    second: RHINOCEROS_
  - first:
      1: 100004
    second: RHINOC<PERSON>OS_ Head
  - first:
      1: 100006
    second: RHINOCEROS_ L Calf
  - first:
      1: 100008
    second: RHINOCEROS_ L Clavicle
  - first:
      1: 100010
    second: RHINOCEROS_ L Finger0
  - first:
      1: 100012
    second: RHINOCEROS_ L Foot
  - first:
      1: 100014
    second: RHINOCEROS_ L Forearm
  - first:
      1: 100016
    second: RHINOCEROS_ L Hand
  - first:
      1: 100018
    second: RHINOCEROS_ L HorseLink
  - first:
      1: 100020
    second: RHINOCEROS_ L Thigh
  - first:
      1: 100022
    second: RHINOCEROS_ L UpperArm
  - first:
      1: 100024
    second: RHINOCEROS_ Neck
  - first:
      1: 100026
    second: RHINOCEROS_ Neck1
  - first:
      1: 100028
    second: RHINOCEROS_ Pelvis
  - first:
      1: 100030
    second: RHINOCEROS_ Queue de cheval 1
  - first:
      1: 100032
    second: RHINOCEROS_ R Calf
  - first:
      1: 100034
    second: RHINOCEROS_ R Clavicle
  - first:
      1: 100036
    second: RHINOCEROS_ R Finger0
  - first:
      1: 100038
    second: RHINOCEROS_ R Foot
  - first:
      1: 100040
    second: RHINOCEROS_ R Forearm
  - first:
      1: 100042
    second: RHINOCEROS_ R Hand
  - first:
      1: 100044
    second: RHINOCEROS_ R HorseLink
  - first:
      1: 100046
    second: RHINOCEROS_ R Thigh
  - first:
      1: 100048
    second: RHINOCEROS_ R UpperArm
  - first:
      1: 100050
    second: RHINOCEROS_ Spine
  - first:
      1: 100052
    second: RHINOCEROS_ Spine1
  - first:
      1: 100054
    second: RHINOCEROS_ Tail
  - first:
      1: 100056
    second: RHINOCEROS_ Tail1
  - first:
      1: 100058
    second: RHINOCEROS_ Tail2
  - first:
      1: 100060
    second: RHINOCEROS_ Tail3
  - first:
      1: 100062
    second: RHINOCEROS_ Tail4
  - first:
      1: 100064
    second: RHINOCEROS_ Tail5
  - first:
      1: 100066
    second: root
  - first:
      1: 100068
    second: Sk_Rhinoceros_LOD0
  - first:
      1: 100070
    second: Sk_Rhinoceros_LOD1
  - first:
      1: 100072
    second: Sk_Rhinoceros_LOD2
  - first:
      4: 400000
    second: //RootNode
  - first:
      4: 400002
    second: RHINOCEROS_
  - first:
      4: 400004
    second: RHINOCEROS_ Head
  - first:
      4: 400006
    second: RHINOCEROS_ L Calf
  - first:
      4: 400008
    second: RHINOCEROS_ L Clavicle
  - first:
      4: 400010
    second: RHINOCEROS_ L Finger0
  - first:
      4: 400012
    second: RHINOCEROS_ L Foot
  - first:
      4: 400014
    second: RHINOCEROS_ L Forearm
  - first:
      4: 400016
    second: RHINOCEROS_ L Hand
  - first:
      4: 400018
    second: RHINOCEROS_ L HorseLink
  - first:
      4: 400020
    second: RHINOCEROS_ L Thigh
  - first:
      4: 400022
    second: RHINOCEROS_ L UpperArm
  - first:
      4: 400024
    second: RHINOCEROS_ Neck
  - first:
      4: 400026
    second: RHINOCEROS_ Neck1
  - first:
      4: 400028
    second: RHINOCEROS_ Pelvis
  - first:
      4: 400030
    second: RHINOCEROS_ Queue de cheval 1
  - first:
      4: 400032
    second: RHINOCEROS_ R Calf
  - first:
      4: 400034
    second: RHINOCEROS_ R Clavicle
  - first:
      4: 400036
    second: RHINOCEROS_ R Finger0
  - first:
      4: 400038
    second: RHINOCEROS_ R Foot
  - first:
      4: 400040
    second: RHINOCEROS_ R Forearm
  - first:
      4: 400042
    second: RHINOCEROS_ R Hand
  - first:
      4: 400044
    second: RHINOCEROS_ R HorseLink
  - first:
      4: 400046
    second: RHINOCEROS_ R Thigh
  - first:
      4: 400048
    second: RHINOCEROS_ R UpperArm
  - first:
      4: 400050
    second: RHINOCEROS_ Spine
  - first:
      4: 400052
    second: RHINOCEROS_ Spine1
  - first:
      4: 400054
    second: RHINOCEROS_ Tail
  - first:
      4: 400056
    second: RHINOCEROS_ Tail1
  - first:
      4: 400058
    second: RHINOCEROS_ Tail2
  - first:
      4: 400060
    second: RHINOCEROS_ Tail3
  - first:
      4: 400062
    second: RHINOCEROS_ Tail4
  - first:
      4: 400064
    second: RHINOCEROS_ Tail5
  - first:
      4: 400066
    second: root
  - first:
      4: 400068
    second: Sk_Rhinoceros_LOD0
  - first:
      4: 400070
    second: Sk_Rhinoceros_LOD1
  - first:
      4: 400072
    second: Sk_Rhinoceros_LOD2
  - first:
      43: 4300000
    second: Sk_Rhinoceros_LOD0
  - first:
      43: 4300002
    second: Sk_Rhinoceros_LOD2
  - first:
      43: 4300004
    second: Sk_Rhinoceros_LOD1
  - first:
      74: 7400000
    second: Take 001
  - first:
      95: 9500000
    second: //RootNode
  - first:
      137: 13700000
    second: Sk_Rhinoceros_LOD0
  - first:
      137: 13700002
    second: Sk_Rhinoceros_LOD1
  - first:
      137: 13700004
    second: Sk_Rhinoceros_LOD2
  - first:
      205: 20500000
    second: //RootNode
  externalObjects: {}
  materials:
    materialImportMode: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 0
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 0
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations: []
    isReadable: 1
  meshes:
    lODScreenPercentages:
    - 0.25
    - 0.125
    - 0.01
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 0
    importBlendShapes: 1
    importCameras: 0
    importLights: 0
    fileIdsGeneration: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    meshOptimizationFlags: -1
    indexFormat: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 0
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 4
    normalCalculationMode: 0
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 1
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips:
  - 1c2fa754badd1504bad8220571f5797c
  - 54400bf4b53c9ba43870d4479ddb4614
  - 50e3ae8ca408e77428e96fcfc18aeca9
  - 45ab6e4a94741304ba86072db227962e
  - d7af5ccde277ae147a31900e907b1c00
  - 1ccaf383383f3db489d67dbceef559f0
  - d7143f997c269734eaac5ea35debb706
  - f977b9843fb53864987c62f428f68e43
  - d99027520a0a92140aea575528132128
  - e87e97132b383224f9e2cedd1c336cec
  - aa608a4a359c5894da48d1bf6bc41b5a
  - 8e59f982ddcbe56448bf46166c6001af
  - 25f2d70d72ace6b4a9708e1f8c5562f1
  - 31a09f011ea616b4d8650dfbf24a383a
  - 5c6448596291cee46b24a1276ef02089
  - 5d409adffa131204183170d7e2238362
  - b5c8ab6df5f00e04a8f25dede6ad66dc
  - ce2af32abd27a0545bbd88d95d8448c6
  - 6d99405224e0940499389d81c7d4d69a
  - 241ba101ffdecb94aa385eba2809fc2f
  - 25d87f4d978162f4b91f0c16864461c0
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 1
    rootMotionBoneName: root
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 0
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 2
  humanoidOversampling: 1
  avatarSetup: 1
  addHumanoidExtraRootOnlyWhenUsingAvatar: 0
  remapMaterialsIfMaterialImportModeIsNone: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
