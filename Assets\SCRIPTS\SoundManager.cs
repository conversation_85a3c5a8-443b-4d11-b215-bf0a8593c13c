using UnityEngine;

/// <summary>
/// Manages sound functionalities, engine disable/enable, and hand hint visibility.
/// </summary>
public class SoundManager : MonoBehaviour
{
    [SerializeField] private GameObject powerxButton,completepannel,pausepannel,failpannel;
    [SerializeField] private GameObject handHint;
    [SerializeField] private GameObject playerObject;

    void Update()
    {
        if(powerxButton.activeSelf == true)
        {
            handHint.SetActive(false);
        }
        else if(completepannel.activeSelf == true  || failpannel.activeSelf == true)
        {
            playerObject.GetComponent<RCC_CarControllerV3>().engineRunning = false;
        }
    }
    }
