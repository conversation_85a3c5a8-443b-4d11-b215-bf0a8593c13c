using SWS;
using System.Collections;
using System.Collections.Generic;
using System.Security.Cryptography;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.SceneManagement;

public class Gamplay_Scripts : MonoBehaviour
{
    public GameObject[] Levels, position, L1Stuff, L6Stuff;
    public GameObject Player, Jeep, rcccam, L1cam, CompletePanel, Controls, L2Container, L2cam, cocodi, L3Stuff, L3cam, Ducs, StartEngine, Ship, DummyTruck, L4cam, BlackScreen, L6cam, L6cam2;
    public GameObject dolphin, Cage, Cagecoco, L8Cam, L8Cam2, L8Dummytruck, L8Dummytruck2, ConcareteTube, Sandbage, VicteryPanel, Skip, bgsound, LL6cam, skippbutton, Playerr
        , rccccame, textbar, endcam, CompletePanel2, <PERSON>, Crane1, crane2, crane3, bgsound1, loadinnl;
    public Animator L1<PERSON><PERSON>, <PERSON>6<PERSON><PERSON>, Cocodianimator, CocoDiEnd;
    public AudioSource FractionSound, VictorySound;
    public Text Cash, lno;
    int i, clickCounter;
    public Material rainsky, windsky;
    public static Gamplay_Scripts intance;
    public GameObject text1, text2;
    void Start()
    {
          Jeep.GetComponent<RCC_CarControllerV3>().engineRunning = false;
        Player.GetComponent<RCC_CarControllerV3>().engineRunning = false;
        if (intance == null)
        {
            intance = this;
        }
        Jeep.GetComponent<Rigidbody>().isKinematic = true;

        Screen.sleepTimeout = SleepTimeout.NeverSleep;
        Time.timeScale = 1f;
        AudioListener.volume = 1f;
        Application.targetFrameRate = 0;
        Player.gameObject.GetComponent<RCC_CarControllerV3>().maxEngineSoundVolume = 0f;
        Jeep.gameObject.GetComponent<RCC_CarControllerV3>().maxEngineSoundVolume = 0f;
        Player.transform.position = position[Level_Selection.LevelNO].transform.position;
        Player.transform.rotation = position[Level_Selection.LevelNO].transform.rotation;
        Jeep.transform.position = position[Level_Selection.LevelNO].transform.position;
        Jeep.transform.rotation = position[Level_Selection.LevelNO].transform.rotation;
        Levels[Level_Selection.LevelNO].gameObject.SetActive(true);
        if (Level_Selection.LevelNO == 0)
        {
            Jeep.SetActive(false);
            Player.SetActive(true);
            StartCoroutine(Level1Seen());
        }
        if (Level_Selection.LevelNO == 1)
        {
            Player.SetActive(true);
            L2Container.gameObject.SetActive(true);
        }
        if (Level_Selection.LevelNO == 2)
        {
            Player.SetActive(true);
            L3Stuff.gameObject.SetActive(true);
        }
        if (Level_Selection.LevelNO == 3)
        {
            Player.SetActive(true);
            Ship.gameObject.SetActive(true);
        }
        if (Level_Selection.LevelNO == 4)
        {
            Player.SetActive(true);
        }
        if (Level_Selection.LevelNO == 5)
        {
            Player.SetActive(true);
            StartCoroutine(Level6Sceen());
        }
        if (Level_Selection.LevelNO == 6)
        {
            Player.SetActive(true);
            Cage.SetActive(true);
            Cagecoco.SetActive(false);
        }
        if (Level_Selection.LevelNO == 7)
        {
            StartEngine.SetActive(false);
            StartCoroutine(Level8Sceen());
        }
        if (Level_Selection.LevelNO == 8)
        {
            Player.SetActive(true);
            ConcareteTube.SetActive(true);
        }
        if (Level_Selection.LevelNO == 9)
        {
            Player.SetActive(true);
            Sandbage.SetActive(true);
        }
        if (Level_Selection.LevelNO == 10 || Level_Selection.LevelNO == 11 || Level_Selection.LevelNO == 14)
        {
            rcccam.GetComponent<RCC_Camera>().TPSDistance = 26f;
            rcccam.GetComponent<RCC_Camera>().TPSHeight = 14f;
            Jeep.SetActive(true);
            textbar.SetActive(true);
        }
        if (Level_Selection.LevelNO == 12 || Level_Selection.LevelNO == 13)
        {
            rcccam.GetComponent<RCC_Camera>().TPSDistance = 26f;
            rcccam.GetComponent<RCC_Camera>().TPSHeight = 14f;
            Jeep.SetActive(true);
        }

    }
    public IEnumerator Level1Seen()
    {
        Skip.SetActive(true);
        StartEngine.SetActive(false);
        rcccam.SetActive(false);
        L1cam.SetActive(true);
        yield return new WaitForSeconds(21f);
        L1Crane.GetComponent<Animator>().enabled = true;
        yield return new WaitForSeconds(2f);
        L1Stuff[0].SetActive(false);
        L1Stuff[1].SetActive(true);
        yield return new WaitForSeconds(8f);
        L1Stuff[1].SetActive(false);
        L1Stuff[2].SetActive(true);
        L1Crane.GetComponent<Animator>().enabled = false;
        yield return new WaitForSeconds(4f);
        rcccam.SetActive(true);
        L1cam.SetActive(false);
        Skip.SetActive(false);
        Crane.SetActive(false);
        Crane1.SetActive(true);
        StartEngine.SetActive(true);
    }
    public IEnumerator L2Sceen()
    {
        Skip.SetActive(true);
        Controls.SetActive(false);
        rcccam.SetActive(false);
        L2cam.SetActive(true);
        yield return new WaitForSeconds(3f);
        cocodi.transform.GetChild(0).GetComponent<Animator>().SetBool("walk", true);
        cocodi.transform.GetChild(0).GetComponent<splineMove>().enabled = true;
        yield return new WaitForSeconds(3f);
        cocodi.transform.GetChild(1).GetComponent<Animator>().SetBool("walk", true);
        cocodi.transform.GetChild(1).GetComponent<splineMove>().enabled = true;
        yield return new WaitForSeconds(23f);
        rcccam.SetActive(true);
        L2cam.SetActive(false);
        Player.GetComponent<Rigidbody>().drag = 0.02f;
        Controls.SetActive(true);
        Skip.SetActive(false);
    }
    public IEnumerator L3Duc()
    {
        Skip.SetActive(true);
        Controls.SetActive(false);
        rcccam.SetActive(false);
        Ducs.SetActive(true);
        L3cam.SetActive(true);
        yield return new WaitForSeconds(20f);
        rcccam.SetActive(true);
        L3cam.SetActive(false);
        Player.GetComponent<Rigidbody>().drag = 0.02f;
        Controls.SetActive(true);
        Skip.SetActive(false);
    }
    public void engn()
    {
        Player.gameObject.GetComponent<RCC_CarControllerV3>().maxEngineSoundVolume = 1f;
        Jeep.gameObject.GetComponent<RCC_CarControllerV3>().maxEngineSoundVolume = 1f;
        Jeep.GetComponent<RCC_CarControllerV3>().engineRunning = true;
        Player.GetComponent<RCC_CarControllerV3>().engineRunning = true;
        Jeep.GetComponent<Rigidbody>().isKinematic = false;
    }
    public IEnumerator L4Cutsceen()
    {
        Skip.SetActive(true);
        Controls.SetActive(false);
        BlackScreen.SetActive(true);
        yield return new WaitForSeconds(1.5f);
        Player.SetActive(false);
        DummyTruck.SetActive(true);
        rcccam.SetActive(false);
        L4cam.SetActive(true);
        BlackScreen.SetActive(false);
        FractionSound.Play();
        StartCoroutine(delay());
    }
    public IEnumerator Level6Sceen()
    {
        StartEngine.SetActive(false);
        rcccam.SetActive(false);
        L6cam.SetActive(true);
        L6Crane.GetComponent<Animator>().enabled = true;
        yield return new WaitForSeconds(3f);
        L6Stuff[0].SetActive(false);
        L6Stuff[1].SetActive(true);
        yield return new WaitForSeconds(6.5f);
        BlackScreen.SetActive(true);
        crane2.SetActive(false);
        crane3.SetActive(true);
        L6Stuff[1].SetActive(false);
        L6Stuff[2].SetActive(true);
        L6Crane.GetComponent<Animator>().enabled = false;
        yield return new WaitForSeconds(1.5f);
        rcccam.SetActive(true);
        L6cam.SetActive(false);
        StartEngine.SetActive(true);
        BlackScreen.SetActive(false);
    }
    public IEnumerator L6Fish()
    {
        Skip.SetActive(true);
        Controls.SetActive(false);
        yield return new WaitForSeconds(2f);
        rcccam.SetActive(false);
        L6cam2.SetActive(true);
        dolphin.SetActive(true);
        yield return new WaitForSeconds(20f);
        Player.GetComponent<Rigidbody>().drag = 0.02f;
        rcccam.SetActive(true);
        L6cam2.SetActive(false);
        Controls.SetActive(true);
        Skip.SetActive(false);
    }
    public IEnumerator Level8Sceen()
    {
        rcccam.SetActive(false);
        L8Cam.SetActive(true);
        Cocodianimator.GetComponent<Animator>().SetBool("walk", true);
        Cocodianimator.GetComponent<splineMove>().enabled = true;
        yield return new WaitForSeconds(18f);
        Cocodianimator.GetComponent<Animator>().SetBool("walk", false);
        BlackScreen.SetActive(true);
        L8Dummytruck.SetActive(false);
        Cocodianimator.gameObject.SetActive(false);
        Player.SetActive(true);
        Cage.SetActive(true);
        Cagecoco.SetActive(true);
        yield return new WaitForSeconds(2f);
        rcccam.SetActive(true);
        L8Cam.SetActive(false);
        BlackScreen.SetActive(false);
        StartEngine.SetActive(true);
    }
    public IEnumerator L8Fish()
    {
        Controls.SetActive(false);
        BlackScreen.SetActive(true);
        BlackScreen.GetComponent<Animator>().SetBool("true", false);
        yield return new WaitForSeconds(2f);
        Player.transform.position = position[0].transform.position;
        Player.transform.rotation = position[0].transform.rotation;
        L8Dummytruck2.SetActive(true);
        rcccam.SetActive(false);
        L8Cam2.SetActive(true);
        BlackScreen.SetActive(false);
        CocoDiEnd.GetComponent<Animator>().SetBool("walk", true);
        yield return new WaitForSeconds(10f);
        StartCoroutine(LevelComplete());
    }
    public IEnumerator delay()
    {
        yield return new WaitForSeconds(10f);
        FractionSound.Stop();
        yield return new WaitForSeconds(5f);
        Skip.SetActive(false);
        StartCoroutine(LevelComplete());
    }
    public IEnumerator LevelComplete()
    {
        Controls.SetActive(false);
        VictorySound.Play();
        VicteryPanel.SetActive(true);
        yield return new WaitForSeconds(5f);
        VicteryPanel.SetActive(false);
        CompletePanel.SetActive(true);
        bgsound.SetActive(false);
        bgsound1.SetActive(false);
        PlayerPrefs.SetInt("level" + Level_Selection.LevelNO, 1);
        PlayerPrefs.SetInt("TotalCash", PlayerPrefs.GetInt("TotalCash") + 100);
        Cash.text = PlayerPrefs.GetInt("TotalCash").ToString();

    }
    public void Nextm2()
    {
        // Increment the current level number
        Level_Selection.LevelNO++;

        // Unlock the next level in PlayerPrefs
        if (Level_Selection.LevelNO < 15) // Assuming there are 15 levels in total
        {
            PlayerPrefs.SetInt("level" + Level_Selection.LevelNO, 1); // Unlock the next level
            PlayerPrefs.Save(); // Save the changes to PlayerPrefs
        }

        // Check if the current level is the last one
        if (Level_Selection.LevelNO == 15)
        {
            StartCoroutine(LoadSceneWithoutStoppingAnimations("ManMenu")); // Go to the main menu (or any final screen)
        }
        else
        {
            StartCoroutine(LoadSceneWithoutStoppingAnimations("SampleScene")); // Load the next level
        }

        // Reset audio volume
        AudioListener.volume = 1f;
    }

    // Coroutine to load scene without stopping animations
    private IEnumerator LoadSceneWithoutStoppingAnimations(string sceneName)
    {
        // Start loading the scene asynchronously but don't activate it yet
        AsyncOperation asyncLoad = SceneManager.LoadSceneAsync(sceneName);
        asyncLoad.allowSceneActivation = false;

        // Wait until the scene is loaded to 90%
        while (asyncLoad.progress < 0.9f)
        {
            yield return null;
        }

        // Now allow the scene to activate
        asyncLoad.allowSceneActivation = true;
    }

    public void Pause()
    {
        Time.timeScale = 0f;


    }
    public void rain()
    {
        RenderSettings.skybox = rainsky;
    }
    public void air()
    {
        RenderSettings.skybox = windsky;
    }
    public AudioSource gameMusic;
    public void Resume()
    {
        Time.timeScale = 1f;
        AudioListener.volume = 1f;
        gameMusic.Play();

    }
    public void Restart()
    {
        StartCoroutine(LoadSceneWithoutStoppingAnimations(SceneManager.GetActiveScene().name));
        AudioListener.volume = 1f;

    }
    public void Next()
    {
        Level_Selection.LevelNO++;

        if (Level_Selection.LevelNO < 10)
        {
            PlayerPrefs.SetInt("level" + Level_Selection.LevelNO, 1);
            PlayerPrefs.Save();
        }

        if (Level_Selection.LevelNO == 10)
        {
            StartCoroutine(LoadSceneWithoutStoppingAnimations("ManMenu"));
        }
        else
        {
            StartCoroutine(LoadSceneWithoutStoppingAnimations("SampleScene"));
        }

        AudioListener.volume = 1f;
    }
    private bool adPlayed = false;
    private bool adhigh = false;

    void Awake()
    {
        // Reset adPlayed state when the game restarts
        adPlayed = PlayerPrefs.GetInt("AdPlayed", 0) == 1;
        adhigh = PlayerPrefs.GetInt("adhigh", 0) == 1;
        if (PlayerPrefs.GetInt("text1", 0) == 1)
        {
            text1.SetActive(false);
        }
        if (PlayerPrefs.GetInt("text2", 0) == 1)
        {
            text2.SetActive(false);
        }
    }

    public void Adplay()
    {
        if (!adPlayed)
        {
            AdsController.Instance.ShowRewardedInterstitialAd_Admob(SkipAd);
            adPlayed = true;
            text1.SetActive(false);
            PlayerPrefs.SetInt("text1", 1);
            PlayerPrefs.SetInt("AdPlayed", 1); // Save the ad played state in PlayerPrefs
            PlayerPrefs.Save(); // Save the changes to PlayerPrefs
        }
        else
        {
            SkipBtn();
            LL6cam.SetActive(false);
            skippbutton.SetActive(false);
            Playerr.SetActive(true);
        }
    }

    public void SkipAd()
    {
        SkipBtn();
        LL6cam.SetActive(false);
        skippbutton.SetActive(false);
        Playerr.SetActive(true);
    }

    public void SkipBtn()
    {
        if (Level_Selection.LevelNO == 0)
        {
            L1Stuff[2].SetActive(true);
            L1Stuff[1].SetActive(false);
            L1Stuff[0].SetActive(false);
            L1Crane.GetComponent<Animator>().enabled = false;
            rcccam.SetActive(true);
            L1cam.SetActive(false);
            StartEngine.SetActive(true);
            StopAllCoroutines();
        }
        if (Level_Selection.LevelNO == 1)
        {
            cocodi.gameObject.SetActive(false);
            rcccam.SetActive(true);
            L2cam.SetActive(false);
            Player.GetComponent<Rigidbody>().drag = 0.02f;
            Controls.SetActive(true);
            StopAllCoroutines();
        }
        if (Level_Selection.LevelNO == 2)
        {
            Ducs.SetActive(false);
            rcccam.SetActive(true);
            L3cam.SetActive(false);
            Player.GetComponent<Rigidbody>().drag = 0.02f;
            Controls.SetActive(true);
            StopAllCoroutines();
        }
        if (Level_Selection.LevelNO == 3)
        {
            BlackScreen.SetActive(false);
            FractionSound.Stop();
            rcccam.SetActive(true);
            L4cam.SetActive(false);
            PlayerPrefs.SetInt("TotalCash", PlayerPrefs.GetInt("TotalCash") + 100);
            Cash.text = PlayerPrefs.GetInt("TotalCash").ToString();
            PlayerPrefs.SetInt("Level", Level_Selection.LevelNO + 1);
            VicteryPanel.SetActive(false);
            bgsound.SetActive(false);
            CompletePanel.SetActive(true);
            StopAllCoroutines();
        }
        if (Level_Selection.LevelNO == 5)
        {
            BlackScreen.SetActive(false);
            Player.GetComponent<Rigidbody>().drag = 0.02f;
            rcccam.SetActive(true);
            L6cam2.SetActive(false);
            Controls.SetActive(true);
            rcccam.SetActive(true);
            StopCoroutine(L6Fish());
        }
        if (Level_Selection.LevelNO == 7)
        {
            L8Dummytruck.SetActive(false);
            Cocodianimator.gameObject.SetActive(false);
            Cage.SetActive(true);
            Cagecoco.SetActive(true);
            Player.transform.position = position[10].transform.position;
            Player.transform.rotation = position[10].transform.rotation;
            rcccam.SetActive(true);
            L8Cam.SetActive(false);
            BlackScreen.SetActive(false);
            StartEngine.SetActive(true);
            StopAllCoroutines();
        }
    }
    public IEnumerator endpoint()
    {
        Controls.SetActive(false);
        yield return new WaitForSeconds(0.5f);
        endcam.SetActive(true);
        yield return new WaitForSeconds(7f);
        CompletePanel2.SetActive(true);
        bgsound.SetActive(false);
        bgsound1.SetActive(false);
        PlayerPrefs.SetInt("TotalCash", PlayerPrefs.GetInt("TotalCash") + 100);
        Cash.text = PlayerPrefs.GetInt("TotalCash").ToString();
        PlayerPrefs.SetInt("Level", Level_Selection.LevelNO + 1);

    }
    public void Level()
    {
        StartCoroutine(LevelComplete());
    }
    public void low()
    {
        Player.gameObject.GetComponent<RCC_CarControllerV3>().engineTorque = 1200;
        Player.GetComponent<RCC_CarControllerV3>().maxspeed = 60;
        Jeep.gameObject.GetComponent<RCC_CarControllerV3>().engineTorque = 600;
        Jeep.GetComponent<RCC_CarControllerV3>().maxspeed = 60;
    }
    public void high()
    {
        if (!adhigh)
        {
           AdsController.Instance.ShowRewardedInterstitialAd_Admob(Hight2);
            adhigh = true;
            text2.SetActive(false);
            PlayerPrefs.SetInt("text2", 1);
            PlayerPrefs.SetInt("adhigh", 1);
            PlayerPrefs.Save();
        }
        else
        {

          Hight2();
        }
    }
    public void Hight2()
    {
        Player.gameObject.GetComponent<RCC_CarControllerV3>().engineTorque = 2500;
        Player.GetComponent<RCC_CarControllerV3>().maxspeed = 100;
        Jeep.gameObject.GetComponent<RCC_CarControllerV3>().engineTorque = 2500;
        Jeep.GetComponent<RCC_CarControllerV3>().maxspeed = 100;
    }

    public void Home()
    {
        Time.timeScale = 1f;
        AudioListener.volume = 1f;
        loadinnl.SetActive(true);
        StartCoroutine("mainload");
    }
    IEnumerator mainload()
    {
        yield return new WaitForSeconds(1f);

        yield return new WaitForSeconds(2f);

        Time.timeScale = 0f;
        StartCoroutine(LoadSceneWithoutStoppingAnimations("ManMenu"));
    }
    public void wind()
    {
        rccccame.GetComponent<UB.Simple2dWeatherEffects.Standard.D2FogsPE>().enabled = true;
        rccccame.GetComponent<UB.Simple2dWeatherEffects.Standard.D2FogsNoiseTexPE>().enabled = true;
    }
    public void wind1()
    {
        RenderSettings.skybox = windsky;
        rccccame.GetComponent<UB.Simple2dWeatherEffects.Standard.D2FogsPE>().enabled = false;
        rccccame.GetComponent<UB.Simple2dWeatherEffects.Standard.D2FogsNoiseTexPE>().enabled = false;
    }
    public void streeingControl()
    {
        RCC.SetMobileController(RCC_Settings.MobileController.SteeringWheel);
    }
    public void ButtonsControl()
    {
        RCC.SetMobileController(RCC_Settings.MobileController.TouchScreen);
    }
    public void TiltControl()
    {
        RCC.SetMobileController(RCC_Settings.MobileController.Gyro);
    }
    public void truckAd(string link)
    {
        Application.OpenURL(link);
    }
    public void changcontrol()
    {
        clickCounter++;
        switch (clickCounter)
        {
            case 1:
                RCC.SetMobileController(RCC_Settings.MobileController.Gyro);
                break;
            case 2:
                RCC.SetMobileController(RCC_Settings.MobileController.TouchScreen);
                break;
            case 3:
                RCC.SetMobileController(RCC_Settings.MobileController.SteeringWheel);
                clickCounter = 0;
                break;
        }
    }
    public void Update()
    {
        {
            if (Level_Selection.LevelNO >= 10)
            {
                i = Level_Selection.LevelNO - 9;
            }
            else
            {
                i = Level_Selection.LevelNO + 1;
            }
            lno.text = " " + i.ToString();
        }
    }
}
