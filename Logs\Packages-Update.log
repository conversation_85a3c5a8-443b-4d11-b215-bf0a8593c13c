
=== Thu May 25 09:08:12 2023

Packages were changed.
Update Mode: mergeDefaultDependencies

The following packages were added:
  com.unity.collab-proxy@1.17.2
  com.unity.ide.rider@3.0.15
  com.unity.ide.visualstudio@2.0.16
  com.unity.ide.vscode@1.2.5
  com.unity.modules.ai@1.0.0
  com.unity.modules.androidjni@1.0.0
  com.unity.modules.animation@1.0.0
  com.unity.modules.assetbundle@1.0.0
  com.unity.modules.audio@1.0.0
  com.unity.modules.cloth@1.0.0
  com.unity.modules.director@1.0.0
  com.unity.modules.imageconversion@1.0.0
  com.unity.modules.imgui@1.0.0
  com.unity.modules.jsonserialize@1.0.0
  com.unity.modules.particlesystem@1.0.0
  com.unity.modules.physics@1.0.0
  com.unity.modules.physics2d@1.0.0
  com.unity.modules.screencapture@1.0.0
  com.unity.modules.terrain@1.0.0
  com.unity.modules.terrainphysics@1.0.0
  com.unity.modules.tilemap@1.0.0
  com.unity.modules.ui@1.0.0
  com.unity.modules.uielements@1.0.0
  com.unity.modules.umbra@1.0.0
  com.unity.modules.unityanalytics@1.0.0
  com.unity.modules.unitywebrequest@1.0.0
  com.unity.modules.unitywebrequestassetbundle@1.0.0
  com.unity.modules.unitywebrequestaudio@1.0.0
  com.unity.modules.unitywebrequesttexture@1.0.0
  com.unity.modules.unitywebrequestwww@1.0.0
  com.unity.modules.vehicles@1.0.0
  com.unity.modules.video@1.0.0
  com.unity.modules.vr@1.0.0
  com.unity.modules.wind@1.0.0
  com.unity.modules.xr@1.0.0
  com.unity.test-framework@1.1.31
  com.unity.textmeshpro@3.0.6
  com.unity.timeline@1.4.8
  com.unity.ugui@1.0.0

=== Wed Oct 30 09:07:37 2024

Packages were changed.
Update Mode: updateDependencies

The following packages were updated:
  com.unity.collab-proxy from version 1.17.2 to 2.4.4
  com.unity.ide.rider from version 3.0.15 to 3.0.31
  com.unity.ide.visualstudio from version 2.0.16 to 2.0.22
  com.unity.test-framework from version 1.1.31 to 1.1.33

=== Thu Oct 31 17:46:55 2024

Packages were changed.
Update Mode: updateDependencies

The following packages were updated:
  com.unity.collab-proxy from version 2.4.4 to 2.5.2

=== Thu Oct 31 18:52:15 2024

Packages were changed.
Update Mode: resetToDefaultDependencies

The following packages were added:
  com.unity.visualscripting@1.9.4
The following packages were removed:
  com.unity.toolchain.win-x86_64-linux-x86_64@2.0.6
