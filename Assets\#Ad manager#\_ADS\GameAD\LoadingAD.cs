using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class LoadingAD : MonoBehaviour
{
    void OnEnable()
    {
        StartCoroutine(ShowAD());
       

    }
    IEnumerator ShowAD()
    {
        yield return new WaitForSeconds(1.5f);
        AdsController.Instance.ShowInterstitialAd_Admob();
        
    }
    void OnDisable()
    {
        AdsController.Instance.HideBannerAd_Admob(2);
    }
}
