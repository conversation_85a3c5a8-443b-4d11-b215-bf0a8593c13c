trackSelection = true
refreshSearchWindowsInPlayMode = false
fetchPreview = true
defaultFlags = 0
keepOpen = false
queryFolder = "Assets"
onBoardingDoNotAskAgain = true
showPackageIndexes = false
showStatusBar = false
scopes = {
	"last_search.DD5CDD96" = ""
	"OpenInspectorPreview.DD5CDD96" = "0"
	"currentGroup.DD5CDD96" = "scene"
}
providers = {
	adb = {
		active = false
		priority = 2500
		defaultAction = null
	}
	asset = {
		active = true
		priority = 25
		defaultAction = null
	}
	store = {
		active = true
		priority = 100
		defaultAction = null
	}
	find = {
		active = true
		priority = 25
		defaultAction = null
	}
	log = {
		active = false
		priority = 210
		defaultAction = null
	}
	packages = {
		active = true
		priority = 90
		defaultAction = null
	}
	performance = {
		active = false
		priority = 100
		defaultAction = null
	}
	profilermarkers = {
		active = false
		priority = 100
		defaultAction = null
	}
	scene = {
		active = true
		priority = 50
		defaultAction = null
	}
}
objectSelectors = {
}
recentSearches = [
]
searchItemFavorites = [
]
savedSearchesSortOrder = 0
showSavedSearchPanel = false
hideTabs = false
expandedQueries = [
]
queryBuilder = false
ignoredProperties = "id;name;classname;imagecontentshash"
helperWidgetCurrentArea = "all"
disabledIndexers = ""
minIndexVariations = 2
findProviderIndexHelper = true