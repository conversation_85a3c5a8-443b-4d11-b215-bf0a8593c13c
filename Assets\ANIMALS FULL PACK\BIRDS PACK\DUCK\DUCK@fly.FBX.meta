fileFormatVersion: 2
guid: 87c1bc94ef6d51544a2da73c65d192ee
ModelImporter:
  serializedVersion: 20300
  internalIDToNameTable:
  - first:
      1: 100000
    second: //RootNode
  - first:
      1: 100002
    second: DUCK_
  - first:
      1: 100004
    second: DUCK_ Head
  - first:
      1: 100006
    second: DUCK_ L Calf
  - first:
      1: 100008
    second: DUCK_ L Clavicle
  - first:
      1: 100010
    second: DUCK_ L Foot
  - first:
      1: 100012
    second: DUCK_ L Forearm
  - first:
      1: 100014
    second: DUCK_ L Hand
  - first:
      1: 100016
    second: DUCK_ L HorseLink
  - first:
      1: 100018
    second: DUCK_ L Thigh
  - first:
      1: 100020
    second: DUCK_ L Toe0
  - first:
      1: 100022
    second: DUCK_ L Toe01
  - first:
      1: 100024
    second: DUCK_ L Toe02
  - first:
      1: 100026
    second: DUCK_ L UpperArm
  - first:
      1: 100028
    second: DUCK_ Neck
  - first:
      1: 100030
    second: DUCK_ Neck1
  - first:
      1: 100032
    second: DUCK_ Neck2
  - first:
      1: 100034
    second: <PERSON><PERSON><PERSON>_ <PERSON><PERSON><PERSON>
  - first:
      1: 100036
    second: DUCK_ Queue de cheval 1
  - first:
      1: 100038
    second: DUCK_ R Calf
  - first:
      1: 100040
    second: DUCK_ R Clavicle
  - first:
      1: 100042
    second: DUCK_ R Foot
  - first:
      1: 100044
    second: DUCK_ R Forearm
  - first:
      1: 100046
    second: DUCK_ R Hand
  - first:
      1: 100048
    second: DUCK_ R HorseLink
  - first:
      1: 100050
    second: DUCK_ R Thigh
  - first:
      1: 100052
    second: DUCK_ R Toe0
  - first:
      1: 100054
    second: DUCK_ R Toe01
  - first:
      1: 100056
    second: DUCK_ R Toe02
  - first:
      1: 100058
    second: DUCK_ R UpperArm
  - first:
      1: 100060
    second: DUCK_ Spine
  - first:
      1: 100062
    second: DUCK_ Tail
  - first:
      1: 100064
    second: DUCK_ TailL
  - first:
      1: 100066
    second: DUCK_ TailR
  - first:
      4: 400000
    second: //RootNode
  - first:
      4: 400002
    second: DUCK_
  - first:
      4: 400004
    second: DUCK_ Head
  - first:
      4: 400006
    second: DUCK_ L Calf
  - first:
      4: 400008
    second: DUCK_ L Clavicle
  - first:
      4: 400010
    second: DUCK_ L Foot
  - first:
      4: 400012
    second: DUCK_ L Forearm
  - first:
      4: 400014
    second: DUCK_ L Hand
  - first:
      4: 400016
    second: DUCK_ L HorseLink
  - first:
      4: 400018
    second: DUCK_ L Thigh
  - first:
      4: 400020
    second: DUCK_ L Toe0
  - first:
      4: 400022
    second: DUCK_ L Toe01
  - first:
      4: 400024
    second: DUCK_ L Toe02
  - first:
      4: 400026
    second: DUCK_ L UpperArm
  - first:
      4: 400028
    second: DUCK_ Neck
  - first:
      4: 400030
    second: DUCK_ Neck1
  - first:
      4: 400032
    second: DUCK_ Neck2
  - first:
      4: 400034
    second: DUCK_ Pelvis
  - first:
      4: 400036
    second: DUCK_ Queue de cheval 1
  - first:
      4: 400038
    second: DUCK_ R Calf
  - first:
      4: 400040
    second: DUCK_ R Clavicle
  - first:
      4: 400042
    second: DUCK_ R Foot
  - first:
      4: 400044
    second: DUCK_ R Forearm
  - first:
      4: 400046
    second: DUCK_ R Hand
  - first:
      4: 400048
    second: DUCK_ R HorseLink
  - first:
      4: 400050
    second: DUCK_ R Thigh
  - first:
      4: 400052
    second: DUCK_ R Toe0
  - first:
      4: 400054
    second: DUCK_ R Toe01
  - first:
      4: 400056
    second: DUCK_ R Toe02
  - first:
      4: 400058
    second: DUCK_ R UpperArm
  - first:
      4: 400060
    second: DUCK_ Spine
  - first:
      4: 400062
    second: DUCK_ Tail
  - first:
      4: 400064
    second: DUCK_ TailL
  - first:
      4: 400066
    second: DUCK_ TailR
  - first:
      74: 7400000
    second: fly
  - first:
      111: 11100000
    second: //RootNode
  externalObjects: {}
  materials:
    materialImportMode: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 0
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    importConstraints: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 2
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: fly
      takeName: Take 001
      internalID: 0
      firstFrame: 0
      lastFrame: 20
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 0
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 0
      loopBlendOrientation: 0
      loopBlendPositionY: 0
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 0
      heightFromFeet: 0
      mirror: 0
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 0.01
    meshCompression: 0
    addColliders: 0
    useSRGBMaterialColor: 1
    sortHierarchyByName: 1
    importVisibility: 0
    importBlendShapes: 1
    importCameras: 0
    importLights: 0
    fileIdsGeneration: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    keepQuads: 0
    weldVertices: 1
    bakeAxisConversion: 0
    preserveHierarchy: 0
    skinWeightsMode: 0
    maxBonesPerVertex: 4
    minBoneWeight: 0.001
    meshOptimizationFlags: -1
    indexFormat: 1
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVMarginMethod: 0
    secondaryUVMinLightmapResolution: 40
    secondaryUVMinObjectScale: 1
    secondaryUVPackMargin: 4
    useFileScale: 0
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 4
    normalCalculationMode: 0
    legacyComputeAllNormalsFromSmoothingGroupsWhenMeshHasBlendShapes: 1
    blendShapeNormalImportMode: 1
    normalSmoothingSource: 0
  referencedClips: []
  importAnimation: 1
  humanDescription:
    serializedVersion: 3
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    globalScale: 0.01
    rootMotionBoneName: 
    hasTranslationDoF: 0
    hasExtraRoot: 1
    skeletonHasParents: 0
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  autoGenerateAvatarMappingIfUnspecified: 1
  animationType: 2
  humanoidOversampling: 1
  avatarSetup: 0
  addHumanoidExtraRootOnlyWhenUsingAvatar: 0
  remapMaterialsIfMaterialImportModeIsNone: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
