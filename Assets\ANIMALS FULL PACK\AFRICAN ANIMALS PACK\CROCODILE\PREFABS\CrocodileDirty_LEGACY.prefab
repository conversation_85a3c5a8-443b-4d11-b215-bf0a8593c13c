%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &103910
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 433850}
  m_Layer: 0
  m_Name: CROCODILE_ R Toe1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &433850
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 103910}
  m_LocalRotation: {x: -0.23597135, y: -0.23597139, z: -0.6665715, w: 0.66657144}
  m_LocalPosition: {x: -0.10627258, y: 0.024658585, z: -0.112035364}
  m_LocalScale: {x: 0.99999994, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 407988}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &108960
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 483040}
  m_Layer: 0
  m_Name: CROCODILE_ Pelvis
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &483040
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 108960}
  m_LocalRotation: {x: -0.5, y: 0.5, z: 0.4999993, w: 0.5000007}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 489726}
  - {fileID: 443408}
  - {fileID: 449832}
  - {fileID: 452454}
  m_Father: {fileID: 465992}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &112118
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 498276}
  m_Layer: 0
  m_Name: CROCODILE_ R Toe0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &498276
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 112118}
  m_LocalRotation: {x: 0.00000000939455, y: -0.00000002221559, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -0.10627256, y: 0.16671973, z: 0.008000869}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 407988}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &116390
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 498808}
  - component: {fileID: 13742678}
  m_Layer: 0
  m_Name: SK_Crocodile_LOD2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &498808
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 116390}
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 494042}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &13742678
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 116390}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: be42e203c3a04bb4090aede4ba668d46, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 4
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 4300004, guid: 3f420de16bb280844a29cc4e4785c9f6, type: 3}
  m_Bones:
  - {fileID: 443776}
  - {fileID: 473590}
  - {fileID: 435930}
  - {fileID: 464646}
  - {fileID: 494554}
  - {fileID: 450736}
  - {fileID: 400404}
  - {fileID: 492184}
  - {fileID: 489726}
  - {fileID: 460014}
  - {fileID: 497820}
  - {fileID: 459230}
  - {fileID: 405424}
  - {fileID: 452636}
  - {fileID: 414496}
  - {fileID: 426836}
  - {fileID: 460670}
  - {fileID: 498170}
  - {fileID: 486006}
  - {fileID: 438906}
  - {fileID: 449832}
  - {fileID: 479364}
  - {fileID: 433850}
  - {fileID: 498276}
  - {fileID: 483040}
  - {fileID: 417108}
  - {fileID: 443408}
  - {fileID: 477080}
  - {fileID: 407988}
  - {fileID: 452454}
  - {fileID: 426792}
  - {fileID: 448234}
  - {fileID: 430930}
  - {fileID: 442220}
  - {fileID: 496370}
  - {fileID: 405268}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 483040}
  m_AABB:
    m_Center: {x: 0.17363024, y: 0.28062844, z: -0.042549074}
    m_Extent: {x: 3.218761, y: 1.1037246, z: 1.2966192}
  m_DirtyAABB: 0
--- !u!1 &117694
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 419024}
  - component: {fileID: 13728566}
  m_Layer: 0
  m_Name: SK_Crocodile_LOD1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &419024
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 117694}
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 494042}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &13728566
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 117694}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: be42e203c3a04bb4090aede4ba668d46, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 4
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 4300002, guid: 3f420de16bb280844a29cc4e4785c9f6, type: 3}
  m_Bones:
  - {fileID: 443776}
  - {fileID: 452636}
  - {fileID: 405424}
  - {fileID: 498170}
  - {fileID: 414496}
  - {fileID: 449832}
  - {fileID: 400404}
  - {fileID: 492184}
  - {fileID: 489726}
  - {fileID: 460014}
  - {fileID: 497820}
  - {fileID: 459230}
  - {fileID: 494554}
  - {fileID: 464646}
  - {fileID: 435930}
  - {fileID: 426836}
  - {fileID: 460670}
  - {fileID: 473590}
  - {fileID: 486006}
  - {fileID: 438906}
  - {fileID: 450736}
  - {fileID: 483040}
  - {fileID: 479364}
  - {fileID: 477080}
  - {fileID: 452454}
  - {fileID: 426792}
  - {fileID: 443408}
  - {fileID: 448234}
  - {fileID: 430930}
  - {fileID: 442220}
  - {fileID: 496370}
  - {fileID: 405268}
  - {fileID: 417108}
  - {fileID: 498276}
  - {fileID: 433850}
  - {fileID: 407988}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 483040}
  m_AABB:
    m_Center: {x: 0.17355895, y: -0.010968208, z: -0.0000015497208}
    m_Extent: {x: 3.2186897, y: 1.0897858, z: 1.2251018}
  m_DirtyAABB: 0
--- !u!1 &122640
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 494042}
  - component: {fileID: 9585080}
  - component: {fileID: 20551228}
  m_Layer: 0
  m_Name: CrocodileDirty_LEGACY
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &494042
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 122640}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 9.98, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 469074}
  - {fileID: 441872}
  - {fileID: 419024}
  - {fileID: 498808}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!95 &9585080
Animator:
  serializedVersion: 3
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 122640}
  m_Enabled: 1
  m_Avatar: {fileID: 9000000, guid: 3f420de16bb280844a29cc4e4785c9f6, type: 3}
  m_Controller: {fileID: 0}
  m_CullingMode: 1
  m_UpdateMode: 0
  m_ApplyRootMotion: 1
  m_LinearVelocityBlending: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorControllerStateOnDisable: 0
--- !u!205 &20551228
LODGroup:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 122640}
  serializedVersion: 2
  m_LocalReferencePoint: {x: 0.0425483, y: 0.4572006, z: -0.8826308}
  m_Size: 6.4375286
  m_FadeMode: 0
  m_AnimateCrossFading: 0
  m_LastLODIsBillboard: 0
  m_LODs:
  - screenRelativeHeight: 0.7350008
    fadeTransitionWidth: 0
    renderers:
    - renderer: {fileID: 13742600}
  - screenRelativeHeight: 0.49071854
    fadeTransitionWidth: 0
    renderers:
    - renderer: {fileID: 13728566}
  - screenRelativeHeight: 0.03617478
    fadeTransitionWidth: 0
    renderers:
    - renderer: {fileID: 13742678}
  m_Enabled: 1
--- !u!1 &122854
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 441872}
  - component: {fileID: 13742600}
  m_Layer: 0
  m_Name: SK_Crocodile_LOD0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &441872
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 122854}
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 494042}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &13742600
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 122854}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: be42e203c3a04bb4090aede4ba668d46, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 4
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 4300000, guid: 3f420de16bb280844a29cc4e4785c9f6, type: 3}
  m_Bones:
  - {fileID: 443776}
  - {fileID: 452636}
  - {fileID: 405424}
  - {fileID: 498170}
  - {fileID: 414496}
  - {fileID: 449832}
  - {fileID: 400404}
  - {fileID: 492184}
  - {fileID: 489726}
  - {fileID: 460014}
  - {fileID: 497820}
  - {fileID: 459230}
  - {fileID: 494554}
  - {fileID: 464646}
  - {fileID: 435930}
  - {fileID: 426836}
  - {fileID: 460670}
  - {fileID: 473590}
  - {fileID: 486006}
  - {fileID: 438906}
  - {fileID: 450736}
  - {fileID: 483040}
  - {fileID: 479364}
  - {fileID: 477080}
  - {fileID: 452454}
  - {fileID: 426792}
  - {fileID: 443408}
  - {fileID: 448234}
  - {fileID: 430930}
  - {fileID: 442220}
  - {fileID: 496370}
  - {fileID: 405268}
  - {fileID: 417108}
  - {fileID: 498276}
  - {fileID: 433850}
  - {fileID: 407988}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 483040}
  m_AABB:
    m_Center: {x: 0.17355895, y: -0.010968208, z: -0.0000015497208}
    m_Extent: {x: 3.2186897, y: 1.0897858, z: 1.2251018}
  m_DirtyAABB: 0
--- !u!1 &123130
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 464646}
  m_Layer: 0
  m_Name: CROCODILE_ L Finger0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &464646
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 123130}
  m_LocalRotation: {x: 0.4731124, y: -0.5439806, z: -0.078511424, w: 0.68853885}
  m_LocalPosition: {x: -0.027628899, y: 0.036188506, z: -0.111407734}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1}
  m_Children: []
  m_Father: {fileID: 492184}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &123626
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 417108}
  m_Layer: 0
  m_Name: CROCODILE_ R Hand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &417108
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 123626}
  m_LocalRotation: {x: 0.55814666, y: -0.17426749, z: 0.28313932, w: 0.7602206}
  m_LocalPosition: {x: -0.38797894, y: 0.000000038146972, z: 0.000000076293944}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_Children:
  - {fileID: 486006}
  - {fileID: 438906}
  m_Father: {fileID: 477080}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &130732
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 486006}
  m_Layer: 0
  m_Name: CROCODILE_ R Finger0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &486006
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 130732}
  m_LocalRotation: {x: -0.47311246, y: 0.54398066, z: -0.07851141, w: 0.68853885}
  m_LocalPosition: {x: -0.027628899, y: 0.036188506, z: 0.11140762}
  m_LocalScale: {x: 1, y: 0.99999994, z: 1.0000001}
  m_Children: []
  m_Father: {fileID: 417108}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &134330
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 450736}
  m_Layer: 0
  m_Name: CROCODILE_ L Foot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &450736
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 134330}
  m_LocalRotation: {x: -0.032067277, y: 0.12869568, z: 0.33547047, w: 0.93266755}
  m_LocalPosition: {x: -0.37484187, y: -0.000000038146972, z: -0.000000076293944}
  m_LocalScale: {x: 1.0000001, y: 0.99999994, z: 0.99999994}
  m_Children:
  - {fileID: 460014}
  - {fileID: 497820}
  m_Father: {fileID: 473590}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &134402
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 443408}
  m_Layer: 0
  m_Name: CROCODILE_ R Thigh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &443408
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 134402}
  m_LocalRotation: {x: 0.7301271, y: -0.13649747, z: 0.6612023, w: 0.10533045}
  m_LocalPosition: {x: -0.00000045776366, y: -0.0000004959106, z: -0.304369}
  m_LocalScale: {x: 1, y: 0.99999994, z: 1.0000001}
  m_Children:
  - {fileID: 460670}
  m_Father: {fileID: 483040}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &134740
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 407988}
  m_Layer: 0
  m_Name: CROCODILE_ R Foot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &407988
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 134740}
  m_LocalRotation: {x: 0.032067366, y: -0.12869556, z: 0.33547068, w: 0.9326675}
  m_LocalPosition: {x: -0.37484175, y: 0, z: -0.00000015258789}
  m_LocalScale: {x: 1, y: 1, z: 0.99999976}
  m_Children:
  - {fileID: 498276}
  - {fileID: 433850}
  m_Father: {fileID: 460670}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &136278
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 460014}
  m_Layer: 0
  m_Name: CROCODILE_ L Toe0
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &460014
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 136278}
  m_LocalRotation: {x: -0.000000018079106, y: 0.000000018799389, z: -0.7071068, w: 0.7071068}
  m_LocalPosition: {x: -0.10627256, y: 0.16671966, z: -0.008000869}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 450736}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &136844
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 426792}
  m_Layer: 0
  m_Name: CROCODILE_ Tail1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &426792
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 136844}
  m_LocalRotation: {x: 2.6069742e-13, y: 0.000000038003503, z: -0.013701869, w: 0.9999061}
  m_LocalPosition: {x: -0.4328202, y: -0.0003728485, z: -0.0000000010340591}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 448234}
  m_Father: {fileID: 452454}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &137592
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 459230}
  m_Layer: 0
  m_Name: CROCODILE_ L UpperArm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &459230
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 137592}
  m_LocalRotation: {x: -0.14700225, y: -0.14762917, z: 0.058852945, w: 0.9762849}
  m_LocalPosition: {x: -0.19886409, y: 0, z: 0}
  m_LocalScale: {x: 1.0000001, y: 0.99999994, z: 1.0000001}
  m_Children:
  - {fileID: 400404}
  m_Father: {fileID: 435930}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &138928
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 443776}
  m_Layer: 0
  m_Name: CROCODILE_ Head
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &443776
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 138928}
  m_LocalRotation: {x: 0.0000000037679047, y: -0.0000020947957, z: 0.4941888, w: 0.86935467}
  m_LocalPosition: {x: -0.26104409, y: -0.05511772, z: -0.000000080598475}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_Children:
  - {fileID: 426836}
  m_Father: {fileID: 452636}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &141458
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 496370}
  m_Layer: 0
  m_Name: CROCODILE_ Tail5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &496370
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 141458}
  m_LocalRotation: {x: -7.7120805e-14, y: -0.000000022484715, z: 0.008106688, w: 0.99996716}
  m_LocalPosition: {x: -0.4063086, y: -0.0003721237, z: -0.0000000010320218}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 405268}
  m_Father: {fileID: 442220}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &143680
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 438906}
  m_Layer: 0
  m_Name: CROCODILE_ R Finger1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &438906
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 143680}
  m_LocalRotation: {x: 0.00039814418, y: 0, z: -0, w: 0.99999994}
  m_LocalPosition: {x: -0.13394131, y: 0, z: 0.000000038146972}
  m_LocalScale: {x: 0.99999994, y: 0.99999994, z: 0.99999994}
  m_Children: []
  m_Father: {fileID: 417108}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &145376
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 497820}
  m_Layer: 0
  m_Name: CROCODILE_ L Toe1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &497820
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 145376}
  m_LocalRotation: {x: 0.23597133, y: 0.23597138, z: -0.6665715, w: 0.66657144}
  m_LocalPosition: {x: -0.10627256, y: 0.024658585, z: 0.11203552}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children: []
  m_Father: {fileID: 450736}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &146152
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 426836}
  m_Layer: 0
  m_Name: CROCODILE_ Queue de cheval 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &426836
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 146152}
  m_LocalRotation: {x: -0.0000014960824, y: 0.000004550081, z: -0.800934, w: 0.5987526}
  m_LocalPosition: {x: 0.023976745, y: 0.10577575, z: -0.00000017451296}
  m_LocalScale: {x: 0.9999999, y: 0.9999999, z: 1}
  m_Children: []
  m_Father: {fileID: 443776}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &146424
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 435930}
  m_Layer: 0
  m_Name: CROCODILE_ L Clavicle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &435930
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 146424}
  m_LocalRotation: {x: -0.6427875, y: -0.0002569967, z: 0.7660444, w: -0.00030411335}
  m_LocalPosition: {x: -0.5551266, y: -0.00000022888183, z: 0.09126599}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 459230}
  m_Father: {fileID: 414496}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &146846
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 492184}
  m_Layer: 0
  m_Name: CROCODILE_ L Hand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &492184
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 146846}
  m_LocalRotation: {x: -0.55814666, y: 0.17426744, z: 0.28313935, w: 0.7602205}
  m_LocalPosition: {x: -0.38797894, y: 0, z: -0.000000076293944}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1}
  m_Children:
  - {fileID: 464646}
  - {fileID: 494554}
  m_Father: {fileID: 400404}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &151610
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 405268}
  m_Layer: 0
  m_Name: CROCODILE_ Tail6
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &405268
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 151610}
  m_LocalRotation: {x: 1.691182e-13, y: -0.000000010938953, z: 0.003943955, w: 0.99999225}
  m_LocalPosition: {x: -0.46744445, y: -0.00032321928, z: -8.96689e-10}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_Children: []
  m_Father: {fileID: 496370}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &152924
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 494554}
  m_Layer: 0
  m_Name: CROCODILE_ L Finger1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &494554
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 152924}
  m_LocalRotation: {x: -0.00039814418, y: 0.000000029796388, z: 0.000000014913025, w: 0.99999994}
  m_LocalPosition: {x: -0.13394123, y: 0, z: -0.000000038146972}
  m_LocalScale: {x: 1, y: 0.99999994, z: 0.99999994}
  m_Children: []
  m_Father: {fileID: 492184}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &155200
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 400404}
  m_Layer: 0
  m_Name: CROCODILE_ L Forearm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &400404
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 155200}
  m_LocalRotation: {x: -0.0000000058610836, y: 0.000000013700083, z: 0.3933307, w: 0.91939706}
  m_LocalPosition: {x: -0.2990815, y: -0.000000076293944, z: 0.000000076293944}
  m_LocalScale: {x: 1, y: 1, z: 1.0000001}
  m_Children:
  - {fileID: 492184}
  m_Father: {fileID: 459230}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &157706
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 405424}
  m_Layer: 0
  m_Name: CROCODILE_ Neck
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &405424
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 157706}
  m_LocalRotation: {x: 3.632635e-12, y: -0.00000012819756, z: 0.030264545, w: 0.99954194}
  m_LocalPosition: {x: -0.55502385, y: -0.113351285, z: -0.0000001660249}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 452636}
  m_Father: {fileID: 414496}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &158432
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 469074}
  m_Layer: 0
  m_Name: root
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &469074
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 158432}
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 465992}
  m_Father: {fileID: 494042}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &160940
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 452454}
  m_Layer: 0
  m_Name: CROCODILE_ Tail
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &452454
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 160940}
  m_LocalRotation: {x: 0.00000070799524, y: -0.00000069825376, z: 0.9999753, w: -0.007024458}
  m_LocalPosition: {x: 0.24495597, y: 0.01036171, z: 0.00000032975797}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 426792}
  m_Father: {fileID: 483040}
  m_RootOrder: 3
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &166026
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 430930}
  m_Layer: 0
  m_Name: CROCODILE_ Tail3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &430930
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 166026}
  m_LocalRotation: {x: 2.4023868e-13, y: -0.000000004834201, z: 0.0017429335, w: 0.9999985}
  m_LocalPosition: {x: -0.50975156, y: -0.00035743712, z: -9.909854e-10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 442220}
  m_Father: {fileID: 448234}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &168170
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 465992}
  m_Layer: 0
  m_Name: CROCODILE_
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &465992
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 168170}
  m_LocalRotation: {x: 0.50000036, y: -0.4999997, z: 0.4999997, w: 0.50000036}
  m_LocalPosition: {x: -0, y: 0.709, z: 0.599}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 483040}
  m_Father: {fileID: 469074}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &171748
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 449832}
  m_Layer: 0
  m_Name: CROCODILE_ Spine
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &449832
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 171748}
  m_LocalRotation: {x: -0.0000020804762, y: 0.000000693676, z: -0.00039815903, w: 0.99999994}
  m_LocalPosition: {x: -0.34088534, y: 0.024486313, z: 0.00000040609368}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 414496}
  m_Father: {fileID: 483040}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &175160
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 448234}
  m_Layer: 0
  m_Name: CROCODILE_ Tail2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &448234
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 175160}
  m_LocalRotation: {x: -3.211719e-14, y: -0.000000015085531, z: 0.0054389713, w: 0.9999852}
  m_LocalPosition: {x: -0.46838638, y: -0.00040596008, z: -0.0000000011257362}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 430930}
  m_Father: {fileID: 426792}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &178360
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 442220}
  m_Layer: 0
  m_Name: CROCODILE_ Tail4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &442220
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 178360}
  m_LocalRotation: {x: 1.2724312e-14, y: 0.000000014251406, z: -0.005138233, w: 0.9999868}
  m_LocalPosition: {x: -0.4488835, y: -0.00032356262, z: -8.972711e-10}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 496370}
  m_Father: {fileID: 430930}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &178584
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 460670}
  m_Layer: 0
  m_Name: CROCODILE_ R Calf
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &460670
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 178584}
  m_LocalRotation: {x: 0.0000000010945705, y: -0.000000040107672, z: 0.3965819, w: 0.9179993}
  m_LocalPosition: {x: -0.34556174, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 407988}
  m_Father: {fileID: 443408}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &179114
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 498170}
  m_Layer: 0
  m_Name: CROCODILE_ R Clavicle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &498170
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 179114}
  m_LocalRotation: {x: 0.6427875, y: 0.000254872, z: 0.7660444, w: -0.00030589622}
  m_LocalPosition: {x: -0.5551266, y: 0.0000002670288, z: -0.09126599}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 479364}
  m_Father: {fileID: 414496}
  m_RootOrder: 2
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &181784
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 477080}
  m_Layer: 0
  m_Name: CROCODILE_ R Forearm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &477080
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 181784}
  m_LocalRotation: {x: 0.000000009780585, y: -0.0000000039195007, z: 0.39333066, w: 0.91939706}
  m_LocalPosition: {x: -0.29908144, y: -0.000000038146972, z: 0}
  m_LocalScale: {x: 0.9999999, y: 0.9999999, z: 0.99999994}
  m_Children:
  - {fileID: 417108}
  m_Father: {fileID: 479364}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &184222
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 479364}
  m_Layer: 0
  m_Name: CROCODILE_ R UpperArm
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &479364
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 184222}
  m_LocalRotation: {x: 0.14700224, y: 0.1476291, z: 0.05885294, w: 0.976285}
  m_LocalPosition: {x: -0.19886409, y: 0, z: 0}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1.0000001}
  m_Children:
  - {fileID: 477080}
  m_Father: {fileID: 498170}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &186934
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 452636}
  m_Layer: 0
  m_Name: CROCODILE_ Neck1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &452636
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 186934}
  m_LocalRotation: {x: -0.000000003966328, y: -0.000000014079295, z: 0.0033186777, w: 0.9999945}
  m_LocalPosition: {x: -0.26108643, y: -0.0002078247, z: -5.7487337e-10}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_Children:
  - {fileID: 443776}
  m_Father: {fileID: 405424}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &188222
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 414496}
  m_Layer: 0
  m_Name: CROCODILE_ Spine1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &414496
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 188222}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.5551264, y: -0.0004420471, z: -0.0000000012260716}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 435930}
  - {fileID: 405424}
  - {fileID: 498170}
  m_Father: {fileID: 449832}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &189756
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 489726}
  m_Layer: 0
  m_Name: CROCODILE_ L Thigh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &489726
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 189756}
  m_LocalRotation: {x: 0.73012626, y: -0.13649844, z: -0.66120315, w: -0.10532933}
  m_LocalPosition: {x: 0.00000045776366, y: 0.0000004196167, z: 0.304369}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1.0000001}
  m_Children:
  - {fileID: 473590}
  m_Father: {fileID: 483040}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &190748
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 473590}
  m_Layer: 0
  m_Name: CROCODILE_ L Calf
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &473590
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 190748}
  m_LocalRotation: {x: -0.000000012749163, y: 0.000000010724487, z: 0.3965822, w: 0.9179992}
  m_LocalPosition: {x: -0.34556177, y: 0.000000038146972, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_Children:
  - {fileID: 450736}
  m_Father: {fileID: 489726}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
