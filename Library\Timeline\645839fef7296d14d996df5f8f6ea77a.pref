%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &1
MonoBehaviour:
  m_ObjectHideFlags: 61
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d79cb9ecc0d4a6d428ab98a681a33897, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  timeAreaShownRange: {x: -0.040885955, y: 3.6915553}
  trackScale: 1
  playRangeEnabled: 0
  timeAreaPlayRange: {x: 3.4028235e+38, y: 3.4028235e+38}
  windowTime: 0
  verticalScroll: 0
  sequencerHeaderWidth: 284
  m_Keys:
  - {fileID: -8006421223100169478, guid: 645839fef7296d14d996df5f8f6ea77a, type: 2}
  - {fileID: 2063226899317477749, guid: 645839fef7296d14d996df5f8f6ea77a, type: 2}
  - {fileID: -1954030554303719240, guid: 645839fef7296d14d996df5f8f6ea77a, type: 2}
  - {fileID: -7093280236387822802, guid: 645839fef7296d14d996df5f8f6ea77a, type: 2}
  - {fileID: 8145330118966296277, guid: 645839fef7296d14d996df5f8f6ea77a, type: 2}
  - {fileID: 2518177865863980449, guid: 645839fef7296d14d996df5f8f6ea77a, type: 2}
  - {fileID: -8526864978394537161, guid: 645839fef7296d14d996df5f8f6ea77a, type: 2}
  - {fileID: 1018840697462012244, guid: 645839fef7296d14d996df5f8f6ea77a, type: 2}
  - {fileID: -8190640856127679218, guid: 645839fef7296d14d996df5f8f6ea77a, type: 2}
  - {fileID: -5368919529400212184, guid: 645839fef7296d14d996df5f8f6ea77a, type: 2}
  - {fileID: -1516623421092764420, guid: 645839fef7296d14d996df5f8f6ea77a, type: 2}
  - {fileID: 2783482086007689910, guid: 645839fef7296d14d996df5f8f6ea77a, type: 2}
  - {fileID: -2291433736728281374, guid: 645839fef7296d14d996df5f8f6ea77a, type: 2}
  - {fileID: 1403137844768498780, guid: 645839fef7296d14d996df5f8f6ea77a, type: 2}
  - {fileID: -8310677738041533852, guid: 645839fef7296d14d996df5f8f6ea77a, type: 2}
  - {fileID: -2278383333638738408, guid: 645839fef7296d14d996df5f8f6ea77a, type: 2}
  - {fileID: -2785281141854848854, guid: 645839fef7296d14d996df5f8f6ea77a, type: 2}
  - {fileID: -1650474769636425962, guid: 645839fef7296d14d996df5f8f6ea77a, type: 2}
  - {fileID: -476325794326561147, guid: 645839fef7296d14d996df5f8f6ea77a, type: 2}
  - {fileID: 7849062555561009722, guid: 645839fef7296d14d996df5f8f6ea77a, type: 2}
  m_Vals:
  - collapsed: 0
    showMarkers: 1
    showInlineCurves: 0
    inlineAnimationCurveHeight: 100
    lastInlineCurveDataID: -1
    inlineCurvesState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_ClientGUIView: {fileID: 0}
      m_SearchString: 
    inlineCurvesShownAreaInsideMargins:
      serializedVersion: 2
      x: 1
      y: 1
      width: 1
      height: 1
    trackHeightExtension: 0
    m_MarkerTimeStampsKeys: 
    m_MarkerTimeStampsValues: 
  - collapsed: 0
    showMarkers: 1
    showInlineCurves: 0
    inlineAnimationCurveHeight: 100
    lastInlineCurveDataID: -1
    inlineCurvesState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_ClientGUIView: {fileID: 0}
      m_SearchString: 
    inlineCurvesShownAreaInsideMargins:
      serializedVersion: 2
      x: 1
      y: 1
      width: 1
      height: 1
    trackHeightExtension: 0
    m_MarkerTimeStampsKeys: 
    m_MarkerTimeStampsValues: 
  - collapsed: 0
    showMarkers: 1
    showInlineCurves: 0
    inlineAnimationCurveHeight: 100
    lastInlineCurveDataID: -1
    inlineCurvesState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_ClientGUIView: {fileID: 0}
      m_SearchString: 
    inlineCurvesShownAreaInsideMargins:
      serializedVersion: 2
      x: 1
      y: 1
      width: 1
      height: 1
    trackHeightExtension: 0
    m_MarkerTimeStampsKeys: 
    m_MarkerTimeStampsValues: 
  - collapsed: 0
    showMarkers: 1
    showInlineCurves: 0
    inlineAnimationCurveHeight: 100
    lastInlineCurveDataID: -1
    inlineCurvesState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_ClientGUIView: {fileID: 0}
      m_SearchString: 
    inlineCurvesShownAreaInsideMargins:
      serializedVersion: 2
      x: 1
      y: 1
      width: 1
      height: 1
    trackHeightExtension: 0
    m_MarkerTimeStampsKeys: 
    m_MarkerTimeStampsValues: 
  - collapsed: 0
    showMarkers: 1
    showInlineCurves: 0
    inlineAnimationCurveHeight: 100
    lastInlineCurveDataID: -1
    inlineCurvesState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_ClientGUIView: {fileID: 0}
      m_SearchString: 
    inlineCurvesShownAreaInsideMargins:
      serializedVersion: 2
      x: 1
      y: 1
      width: 1
      height: 1
    trackHeightExtension: 0
    m_MarkerTimeStampsKeys: 
    m_MarkerTimeStampsValues: 
  - collapsed: 0
    showMarkers: 1
    showInlineCurves: 0
    inlineAnimationCurveHeight: 100
    lastInlineCurveDataID: -1
    inlineCurvesState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_ClientGUIView: {fileID: 0}
      m_SearchString: 
    inlineCurvesShownAreaInsideMargins:
      serializedVersion: 2
      x: 1
      y: 1
      width: 1
      height: 1
    trackHeightExtension: 0
    m_MarkerTimeStampsKeys: 
    m_MarkerTimeStampsValues: 
  - collapsed: 0
    showMarkers: 1
    showInlineCurves: 0
    inlineAnimationCurveHeight: 100
    lastInlineCurveDataID: -1
    inlineCurvesState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_ClientGUIView: {fileID: 0}
      m_SearchString: 
    inlineCurvesShownAreaInsideMargins:
      serializedVersion: 2
      x: 1
      y: 1
      width: 1
      height: 1
    trackHeightExtension: 0
    m_MarkerTimeStampsKeys: 
    m_MarkerTimeStampsValues: 
  - collapsed: 0
    showMarkers: 1
    showInlineCurves: 0
    inlineAnimationCurveHeight: 100
    lastInlineCurveDataID: -1
    inlineCurvesState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_ClientGUIView: {fileID: 0}
      m_SearchString: 
    inlineCurvesShownAreaInsideMargins:
      serializedVersion: 2
      x: 1
      y: 1
      width: 1
      height: 1
    trackHeightExtension: 0
    m_MarkerTimeStampsKeys: 
    m_MarkerTimeStampsValues: 
  - collapsed: 0
    showMarkers: 1
    showInlineCurves: 0
    inlineAnimationCurveHeight: 100
    lastInlineCurveDataID: -1
    inlineCurvesState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_ClientGUIView: {fileID: 0}
      m_SearchString: 
    inlineCurvesShownAreaInsideMargins:
      serializedVersion: 2
      x: 1
      y: 1
      width: 1
      height: 1
    trackHeightExtension: 0
    m_MarkerTimeStampsKeys: 
    m_MarkerTimeStampsValues: 
  - collapsed: 0
    showMarkers: 1
    showInlineCurves: 0
    inlineAnimationCurveHeight: 100
    lastInlineCurveDataID: -1
    inlineCurvesState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_ClientGUIView: {fileID: 0}
      m_SearchString: 
    inlineCurvesShownAreaInsideMargins:
      serializedVersion: 2
      x: 1
      y: 1
      width: 1
      height: 1
    trackHeightExtension: 0
    m_MarkerTimeStampsKeys: 
    m_MarkerTimeStampsValues: 
  - collapsed: 0
    showMarkers: 1
    showInlineCurves: 0
    inlineAnimationCurveHeight: 100
    lastInlineCurveDataID: -1
    inlineCurvesState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_ClientGUIView: {fileID: 0}
      m_SearchString: 
    inlineCurvesShownAreaInsideMargins:
      serializedVersion: 2
      x: 1
      y: 1
      width: 1
      height: 1
    trackHeightExtension: 0
    m_MarkerTimeStampsKeys: 
    m_MarkerTimeStampsValues: 
  - collapsed: 0
    showMarkers: 1
    showInlineCurves: 0
    inlineAnimationCurveHeight: 100
    lastInlineCurveDataID: -1
    inlineCurvesState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_ClientGUIView: {fileID: 0}
      m_SearchString: 
    inlineCurvesShownAreaInsideMargins:
      serializedVersion: 2
      x: 1
      y: 1
      width: 1
      height: 1
    trackHeightExtension: 0
    m_MarkerTimeStampsKeys: 
    m_MarkerTimeStampsValues: 
  - collapsed: 0
    showMarkers: 1
    showInlineCurves: 0
    inlineAnimationCurveHeight: 100
    lastInlineCurveDataID: -1
    inlineCurvesState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_ClientGUIView: {fileID: 0}
      m_SearchString: 
    inlineCurvesShownAreaInsideMargins:
      serializedVersion: 2
      x: 1
      y: 1
      width: 1
      height: 1
    trackHeightExtension: 0
    m_MarkerTimeStampsKeys: 
    m_MarkerTimeStampsValues: 
  - collapsed: 0
    showMarkers: 1
    showInlineCurves: 0
    inlineAnimationCurveHeight: 100
    lastInlineCurveDataID: -1
    inlineCurvesState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_ClientGUIView: {fileID: 0}
      m_SearchString: 
    inlineCurvesShownAreaInsideMargins:
      serializedVersion: 2
      x: 1
      y: 1
      width: 1
      height: 1
    trackHeightExtension: 0
    m_MarkerTimeStampsKeys: 
    m_MarkerTimeStampsValues: 
  - collapsed: 0
    showMarkers: 1
    showInlineCurves: 0
    inlineAnimationCurveHeight: 100
    lastInlineCurveDataID: -1
    inlineCurvesState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_ClientGUIView: {fileID: 0}
      m_SearchString: 
    inlineCurvesShownAreaInsideMargins:
      serializedVersion: 2
      x: 1
      y: 1
      width: 1
      height: 1
    trackHeightExtension: 0
    m_MarkerTimeStampsKeys: 
    m_MarkerTimeStampsValues: 
  - collapsed: 0
    showMarkers: 1
    showInlineCurves: 0
    inlineAnimationCurveHeight: 100
    lastInlineCurveDataID: -1
    inlineCurvesState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_ClientGUIView: {fileID: 0}
      m_SearchString: 
    inlineCurvesShownAreaInsideMargins:
      serializedVersion: 2
      x: 1
      y: 1
      width: 1
      height: 1
    trackHeightExtension: 0
    m_MarkerTimeStampsKeys: 
    m_MarkerTimeStampsValues: 
  - collapsed: 0
    showMarkers: 1
    showInlineCurves: 0
    inlineAnimationCurveHeight: 100
    lastInlineCurveDataID: -1
    inlineCurvesState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_ClientGUIView: {fileID: 0}
      m_SearchString: 
    inlineCurvesShownAreaInsideMargins:
      serializedVersion: 2
      x: 1
      y: 1
      width: 1
      height: 1
    trackHeightExtension: 0
    m_MarkerTimeStampsKeys: 
    m_MarkerTimeStampsValues: 
  - collapsed: 0
    showMarkers: 1
    showInlineCurves: 0
    inlineAnimationCurveHeight: 100
    lastInlineCurveDataID: -1
    inlineCurvesState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_ClientGUIView: {fileID: 0}
      m_SearchString: 
    inlineCurvesShownAreaInsideMargins:
      serializedVersion: 2
      x: 1
      y: 1
      width: 1
      height: 1
    trackHeightExtension: 0
    m_MarkerTimeStampsKeys: 
    m_MarkerTimeStampsValues: 
  - collapsed: 0
    showMarkers: 1
    showInlineCurves: 0
    inlineAnimationCurveHeight: 100
    lastInlineCurveDataID: -1
    inlineCurvesState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_ClientGUIView: {fileID: 0}
      m_SearchString: 
    inlineCurvesShownAreaInsideMargins:
      serializedVersion: 2
      x: 1
      y: 1
      width: 1
      height: 1
    trackHeightExtension: 0
    m_MarkerTimeStampsKeys: 
    m_MarkerTimeStampsValues: 
  - collapsed: 0
    showMarkers: 1
    showInlineCurves: 0
    inlineAnimationCurveHeight: 100
    lastInlineCurveDataID: -1
    inlineCurvesState:
      scrollPos: {x: 0, y: 0}
      m_SelectedIDs: 
      m_LastClickedID: 0
      m_ExpandedIDs: 
      m_RenameOverlay:
        m_UserAcceptedRename: 0
        m_Name: 
        m_OriginalName: 
        m_EditFieldRect:
          serializedVersion: 2
          x: 0
          y: 0
          width: 0
          height: 0
        m_UserData: 0
        m_IsWaitingForDelay: 0
        m_IsRenaming: 0
        m_OriginalEventType: 11
        m_IsRenamingFilename: 0
        m_ClientGUIView: {fileID: 0}
      m_SearchString: 
    inlineCurvesShownAreaInsideMargins:
      serializedVersion: 2
      x: 1
      y: 1
      width: 1
      height: 1
    trackHeightExtension: 0
    m_MarkerTimeStampsKeys: 
    m_MarkerTimeStampsValues: 
